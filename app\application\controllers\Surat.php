<?php

use chillerlan\QRCode\QRCode;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\TemplateProcessor;
use Spipu\Html2Pdf\Html2Pdf;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Warga_Model $warga
 * @property Surat_Model $surat
 * @property Kontak_Penting $kontakpenting
 * @property Master_Users $masterusers
 * @property Setting_Umum $settingumum
 * @property Kategori_Surat $kategorisurat
 * @property DetailKategori_Surat $detailkategorisurat
 * @property MsLetterAccess $letteraccess
 * @property Model_Surat $modelsurat
 * @property Field_Surat $fieldsurat
 * @property Arsip_Surat $arsip_surat
 * @property CI_Input $input
 * @property CI_DB_mysqli_driver $db
 * @property CI_Upload $upload
 * @property Datatables $datatables
 * @property MsPenandatangan $mspenandatangan
 */
class Surat extends MY_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->load->model('Warga_Model', 'warga');
		$this->load->model('Surat_Model', 'surat');
		$this->load->model('Kontak_Penting', 'kontakpenting');
		$this->load->model('Master_Users', 'masterusers');
		$this->load->model('Setting_Umum', 'settingumum');
		$this->load->model('Kategori_Surat', 'kategorisurat');
		$this->load->model('DetailKategori_Surat', 'detailkategorisurat');
		$this->load->model('MsLetterAccess', 'letteraccess');
		$this->load->model('Model_Surat', 'modelsurat');
		$this->load->model('Field_Surat', 'fieldsurat');
		$this->load->model('Arsip_Surat', 'arsip_surat');
		$this->load->model('MsPenandatangan', 'mspenandatangan');

		include_once './application/libraries/tbszip_2.16/tbszip.php';
	}

	/**
	 * Debug method to inspect file download and validation issues
	 * Usage: /surat/debug_file_download?filename=your_file.docx
	 */
	public function debug_file_download()
	{
		if (!isAdmin()) {
			echo "Access denied";
			return;
		}

		$filename = $this->input->get('filename');
		if (!$filename) {
			echo "Please provide filename parameter";
			return;
		}

		echo "<h3>File Debug Information</h3>";
		echo "<strong>Filename:</strong> " . htmlspecialchars($filename) . "<br>";

		$remote_url = asset_url($filename);
		echo "<strong>Remote URL:</strong> " . htmlspecialchars($remote_url) . "<br>";

		$local_path = './application/cache/' . $filename;
		echo "<strong>Local Path:</strong> " . htmlspecialchars($local_path) . "<br><br>";

		// Test URL accessibility
		$headers = @get_headers($remote_url);
		echo "<strong>URL Headers:</strong><br>";
		if ($headers) {
			foreach ($headers as $header) {
				echo htmlspecialchars($header) . "<br>";
			}
		} else {
			echo "Failed to get headers<br>";
		}

		echo "<br><strong>Download Test:</strong><br>";
		$content = file_get_contents($remote_url);
		if ($content === false) {
			echo "❌ Download failed<br>";
		} else {
			echo "✅ Download successful<br>";
			echo "Content size: " . strlen($content) . " bytes<br>";
			echo "Content type detected: " . (function_exists('mime_content_type') ? 'Available' : 'Not available') . "<br>";

			// Check if content looks like HTML
			if (stripos($content, '<html') !== false || stripos($content, '<!doctype') !== false) {
				echo "⚠️ Content appears to be HTML (error page)<br>";
				echo "First 500 characters:<br>";
				echo "<pre>" . htmlspecialchars(substr($content, 0, 500)) . "</pre>";
			} else {
				echo "✅ Content appears to be binary (likely a real file)<br>";
				echo "First 50 bytes (hex): " . bin2hex(substr($content, 0, 50)) . "<br>";
			}

			// Try to save and validate
			$saved = file_put_contents($local_path, $content);
			if ($saved) {
				echo "✅ File saved successfully<br>";

				if (file_exists($local_path)) {
					echo "✅ File exists after save<br>";
					echo "File size on disk: " . filesize($local_path) . " bytes<br>";

					$ext = pathinfo($filename, PATHINFO_EXTENSION);
					if ($ext == 'docx') {
						$zip = new ZipArchive();
						$result = $zip->open($local_path);
						if ($result === TRUE) {
							echo "✅ File is a valid ZIP archive<br>";
							$zip->close();
						} else {
							echo "❌ File is not a valid ZIP archive (Error code: $result)<br>";
						}
					}
				} else {
					echo "❌ File does not exist after save<br>";
				}

				// Clean up
				if (file_exists($local_path)) {
					unlink($local_path);
				}
			} else {
				echo "❌ Failed to save file<br>";
			}
		}
	}

	public function send_smsgateway($phone, $message)
	{
		$ch = curl_init();

		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, "phone=$phone&message=$message");
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_URL, "https://smsgateway.karpeldevtech.com/api/smsgateway/send");

		$result = curl_exec($ch);
		curl_close($ch);

		return json_decode($result);
	}

	public function index()
	{
		$data = array();
		$data['surat'] = $this->letteraccess->select('b.nama, a.categoryid, b.icon')
			->join('kategori_surat b', 'b.id = a.categoryid')
			->result(array('a.userid' => $this->subdomain_account->id));

		return $this->load->view('surat/index', $data);
	}

	public function cek_nik()
	{
		$nik = getPost('nik');

		if ($nik == null) {
			return JSONResponseDefault('FAILED', 'NIK harus diisi');
		}

		$cek = $this->warga->getDefaultData(array(
			"(a.nik = '$nik' OR a.rfid = '$nik') =" => true,
			'a.id_user' => $this->subdomain_account->id
		));

		if ($cek->num_rows() == 0) {
			echo 0;
		} else {
			echo 1;
		}
	}

	public function api_get()
	{
		$nik = getPost('nik');
		$desaid = getPost('desaid');

		$check = $this->warga->getDefaultData(array(
			"(a.nik = '$nik' OR a.rfid = '$nik') =" => true,
			'a.id_user' => $desaid
		));

		if ($check->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'NIK yang anda masukkan tidak terdaftar');
		}

		$row = $check->row();

		return JSONResponse(array('RESULT' => 'OK', 'DATA' => $row));
	}

	public function form_fields($jenis)
	{
		$model = getPost('model');

		if ($model != 'All') {
			$get = $this->modelsurat->get(array(
				'id' => $model
			));

			if ($get->num_rows() == 0) {
				return JSONResponseDefault('FAILED', 'Model surat tidak ditemukan');
			}

			$fields = $this->fieldsurat->select('a.sequence, a.variablename, a.input_type, a.input_label, a.is_required, a.other, a.isstatement')
				->order_by('a.sequence', 'ASC')
				->group_by('a.sequence, a.variablename, a.input_type, a.input_label, a.is_required, a.other, a.isstatement')
				->result(array(
					'modelid' => $model
				));
		} else {
			$modelGet = $this->modelsurat->get(array('categorylettersid' => $jenis));

			if ($modelGet->num_rows() == 0) {
				return JSONResponseDefault('FAILED', 'Model surat tidak ditemukan');
			}

			$modelid = array();
			foreach ($modelGet->result() as $key => $value) {
				$modelid[] = $value->id;
			}

			$fields = $this->fieldsurat->order_by('sequence', 'ASC')->where_in(array(
				'modelid' => $modelid
			))->result();
		}

		return JSONResponse(array(
			'RESULT' => 'OK',
			'CONTENT' => $this->load->view('surat/fields', array(
				'fields' => $fields
			), true)
		));
	}

	public function create($jenis = null, $nik = null)
	{
		$get = $this->kategorisurat->get(array('a.id' => $jenis));

		if ($get->num_rows() == 0) {
			return redirect(base_url('surat'));
		}

		$row = $get->row();

		$data = array();
		$data['jenis_surat'] = $jenis;
		$data['jenis_surat_text'] = $row->nama;

		if ($row->icon == null) {
			$data['image_jenis'] = asset_url() . "assets/suratku/images/jenis_surat/20210302200055.png";
		} else {
			$data['image_jenis'] = asset_url($row->icon);
		}

		$cek = $this->warga->getDefaultData(array(
			"(a.nik = '$nik' OR a.rfid = '$nik') =" => true,
		));

		if ($cek->num_rows() > 0) {
			$data['cek'] = "1";
			$data['warga'] = $cek->row();
		} else {
			$data['cek'] = "0";
		}

		$data['kategorisurat'] = $this->detailkategorisurat->select('b.id, b.name, b.position AS jabatan')
			->join('mspenandatangan b', 'b.id = a.penandatanganid')
			->group_by('b.id, b.name, b.position')
			->get(array(
				'a.catsuratid' => $jenis,
				'a.id_user' => $this->subdomain_account->id
			));

		$data['model'] = $this->modelsurat->get(array('categorylettersid' => $jenis));
		$data['modelsurat'] = $this->modelsurat;
		$data['fields'] = $this->fieldsurat;
		$data['subdomain'] = $this->subdomain_account;

		return $this->load->view('surat/createv2', $data);
	}

	public function api_create()
	{
		$check = $this->masterusers->getDefaultData(array(
			'a.subdomain' => 'desa'
		));

		if ($check->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
		}

		$row = $check->row();

		$nik = getPost('nik');
		$jenis = getPost('jenis');
		$nomor_hp = getPost('nomor_hp');
		$nomor_surat = getPost('nomor_surat');
		$tanggal_kematian = getPost('tanggal_kematian');
		$jam = getPost('jam');
		$tempat = getPost('tempat');
		$disebabkan = getPost('disebabkan');
		$bidang_usaha = getPost('bidang_usaha');
		$alamat_usaha = getPost('alamat_usaha');
		$tanggal_pindah = getPost('tanggal_pindah');
		$alamat_pindah = getPost('alamat_pindah');
		$desa_pindah = getPost('desa_pindah');
		$kecamatan_pindah = getPost('kecamatan_pindah');
		$kabupaten_pindah = getPost('kabupaten_pindah');
		$provinsi_pindah = getPost('provinsi_pindah');
		$alasan_pindah = getPost('alasan_pindah');
		$penandatangan = getPost('penandatangan');
		$status_pernikahan = getPost('status_pernikahan');
		$istri_ke = getPost('istri_ke');
		$nama_sutri_terdahulu = getPost('nama_sutri_terdahulu');
		$nik_ayah = getPost('nik_ayah');
		$nik_ibu = getPost('nik_ibu');
		$info_cerai = getPost('info_cerai');
		$tipe_surat_nikah = getPost('tipe_surat_nikah');
		$nama_calon_sutri = getPost('nama_calon_sutri');
		$nik_calon_sutri = getPost('nik_calon_sutri');
		$penghasilan = getPost('penghasilan');

		$get_nama = $this->warga->getDefaultData(array('a.nik' => $this->input->post('nik')))->row();

		if ($get_nama == null) {
			return JSONResponse(array('RESULT' => 'FAILED', 'MESSAGE' => 'NIK tidak ditemukan'));
		} else {
			$get_nama = $get_nama->nama;
		}

		if ($nik_ayah != null) {
			$ayah = $this->warga->getDefaultData(array(
				'a.nik' => $nik_ayah
			));

			if ($ayah->num_rows() == 0) {
				return JSONResponseDefault('FAILED', 'NIK Ayah tidak ditemukan');
			} else {
				$ayah = $ayah->row();
			}
		}

		if ($nik_ibu != null) {
			$ibu = $this->warga->getDefaultData(array(
				'a.nik' => $nik_ibu
			));

			if ($ibu->num_rows() == 0) {
				return JSONResponseDefault('FAILED', 'NIK Ibu tidak ditemukan');
			} else {
				$ibu = $ibu->row();
			}
		}

		if ($jenis == 1) {
			$jenis_surat = "Surat Pengantar RT";
			$keperluan = "Untuk mengajukan permohonan Surat Pengantar RT";
		} else if ($jenis == 2) {
			$jenis_surat = "Surat Keterangan Laporan Kehilangan";
			$keperluan = "Untuk mengajukan permohonan Surat Keterangan Laporan Kehilangan " . $this->input->post('keperluan');
		} else if ($jenis == 3) {
			$jenis_surat = "Surat Keterangan Kematian";
			$keperluan = "Untuk mengajukan permohonan Surat Keterangan Kematian";
		} else if ($jenis == 4) {
			$jenis_surat = "Surat Keterangan Tidak Mampu";
			$keperluan = "Untuk mengajukan permohonan Surat Keterangan Tidak Mampu";
		} else if ($jenis == 5) {
			$jenis_surat = "Surat Keterangan Usaha";
			$keperluan = "Untuk mengajukan permohonan Surat Keterangan Usaha";
		} else if ($jenis == 6) {
			$jenis_surat = "Surat Keterangan Pindah";
			$keperluan = "Untuk mengajukan permohonan Surat Keterangan Pindah";
		} else if ($jenis == 7) {
			$jenis_surat = "Surat Keterangan Nikah";
			$keperluan = "Untuk mengajukan permohonan Surat Keterangan Nikah";
		} else if ($jenis == 8) {
			$jenis_surat = "Surat Keterangan Domisili";
			$keperluan = "Untuk mengajukan permohonan Surat Keterangan Domisili";
		} else if ($jenis == 9) {
			$jenis_surat = "Surat Keterangan Penghasilan";
			$keperluan = "Untuk mengajukan permohonan Surat Keterangan Penghasilan";
		} else if ($jenis == 10) {
			$jenis_surat = "Surat Keterangan SKCK";
			$keperluan = "Untuk mengajukan permohonan Surat Keterangan SKCK";
		}

		$data = array(
			'nik' => $nik,
			'jenis_surat' => $jenis,
			'nomor_hp' => $nomor_hp,
			'keperluan' => $keperluan,
			'tanggal_permohonan' => date('Y-m-d H:i:s'),
			'status' => 'Menunggu',
			'tanggal_status' => date('Y-m-d H:i:s'),
			'tanggal_kematian' => $tanggal_kematian,
			'jam' => $jam,
			'tempat' => $tempat,
			'disebabkan' => $disebabkan,
			'bidang_usaha' => $bidang_usaha,
			'alamat_usaha' => $alamat_usaha,
			'tanggal_pindah' => $tanggal_pindah,
			'alamat_pindah' => $alamat_pindah,
			'desa_pindah' => $desa_pindah,
			'kecamatan_pindah' => $kecamatan_pindah,
			'kabupaten_pindah' => $kabupaten_pindah,
			'provinsi_pindah' => $provinsi_pindah,
			'alasan_pindah' => $alasan_pindah,
			'nomor_surat' => $nomor_surat,
			'penandatanganid' => $penandatangan,
			'status_pernikahan' => $status_pernikahan,
			'istri_ke' => $istri_ke,
			'nama_sutri_terdahulu' => $nama_sutri_terdahulu,
			'nik_ayah' => isset($ayah->nik) ? $ayah->nik : null,
			'nik_ibu' => isset($ibu->nik) ? $ibu->nik : null,
			'info_cerai' => $info_cerai,
			'tipe_surat_nikah' => $tipe_surat_nikah,
			'nama_calon_sutri' => $nama_calon_sutri,
			'nik_calon_sutri' => $nik_calon_sutri,
			'penghasilan' => $penghasilan,
			'tipe_surat' => 'otomatis',
		);

		$insert = $this->surat->insert($data);

		if (!$insert) {
			return JSONResponseDefault('FAILED', 'Gagal menambahkan data');
		}

		$id_surat = $this->db->insert_id();
		$link = base_url('surat/permohonan/baru/id/' . $id_surat);

		$json = getPost('json', []);
		if (!is_array($json)) {
			$json = json_decode($json);
		}

		foreach ($json as $key => $value) {
			$detail = array(
				'suratid' => $id_surat,
				'nama' => $value->nama,
				'jenis_kelamin' => $value->jenis_kelamin,
				'umur' => $value->umur,
				'status_perkawinan' => $value->status_perkawinan,
				'pendidikan' => $value->pendidikan,
				'nik' => $value->nik,
				'keterangan' => $value->keterangan
			);

			$this->db->insert('pengikut', $detail);
		}

		$kontak = $this->detailkategorisurat->getDefaultData(array(
			'a.catsuratid' => $jenis,
			'a.id_user' => $row->id,
			'a.id' => $penandatangan
		));

		$message = "Ada permohonan surat baru dari warga anda. Silahkan cek dan lakukan approve dengan cara KLIK link berikut ini : $link";

		foreach ($kontak->result() as $key => $value) {
			$telepon = $value->nomor_handphone;
			sendMessageWhatsapp($telepon, $message);

			$message_sms = "Ada permohonan surat baru dari warga anda.
NIK: " . $nik . "
Nama : $get_nama
Jenis Surat: $jenis_surat
$keperluan

Balas dengan mengetik OK.$id_surat untuk melakukan Approve";

			$this->send_smsgateway($telepon, $message_sms);
		}

		return JSONResponse(array('RESULT' => 'OK', 'MESSAGE' => 'Berhasil menambahkan data'));
	}

	public function process_createv2()
	{
		$jenis_surat = getPost('jenis_surat');
		$nik = getPost('nik');
		$print_surat = getPost('print_surat');
		$nomor_hp = getPost('nomor_hp');
		$penandatangan = getPost('penandatangan');
		$modelsurat = getPost('model');

		$warga = $this->warga->get(array('nik' => $nik));

		if ($warga->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Data warga tidak ditemukan');
		}

		$g_penandatangan = $this->mspenandatangan->get(array(
			'id' => $penandatangan,
			'userid' => $this->subdomain_account->id
		));

		if ($g_penandatangan->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Data penandatangan tidak ditemukan');
		}

		$r_penandatangan = $g_penandatangan->row();

		$d_penandatangan = $this->detailkategorisurat->get(array(
			'catsuratid' => $jenis_surat,
			'nama' => $r_penandatangan->name,
			'id_user' => $this->subdomain_account->id,
			'penandatanganid' => $r_penandatangan->id
		));

		if ($d_penandatangan->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Data penandatangan tidak ditemukan');
		}

		$warga = $warga->row();

		$required_orig = array();
		$fields_post = array();

		foreach (requiredVariable($this->subdomain_account->id, $modelsurat) as $key => $value) {
			$required_orig[$key] = getPost($key);
		}

		$model = $this->modelsurat->get(array('categorylettersid' => $jenis_surat));

		if ($modelsurat != 'All') {
			if ($model->num_rows() == 1) {
				$row = $model->row();
				$modelsurat = $row->id;

				$fields = $this->fieldsurat->order_by('a.sequence', 'ASC')->result(array('modelid' => $row->id));

				foreach ($fields as $key => $value) {
					if ($value->isstatement != 1) {
						if ($value->input_type != 'multiple') {
							$fields_post[$value->variablename] = getPost($value->variablename);
						} else {
							$stored_value = array();
							foreach (json_decode($value->other) as $k => $v) {
								$variable_name = $v->variable_name;
								$post_variable = getPost("$variable_name" . "[]", []);

								$stored_value[$variable_name] = $post_variable;
							}

							$fields_post[$value->variablename] = json_encode($stored_value);
						}
					} else {
						$statement = json_decode($value->statement);

						if ($statement->statement == 'gender') {
							if ($statement->type == 'equals') {
								if (($statement->with == 'Perempuan' && $warga->jenis_kelamin == $statement->with) || ($statement->with == 'Laki-laki' && ($warga->jenis_kelamin == 'Laki-laki' || $warga->jenis_kelamin == 'Laki - laki'))) {
									$fields_post[$value->variablename] = $statement->valid;
								} else {
									$fields_post[$value->variablename] = $statement->invalid;
								}
							}
						}
					}
				}
			} else {
				$model = $this->modelsurat->get(array('id' => $modelsurat, 'categorylettersid' => $jenis_surat));

				if ($model->num_rows() == 0) {
					return JSONResponseDefault('FAILED', 'Model surat tidak ditemukan');
				}

				$row = $model->row();

				$fields = $this->fieldsurat->order_by('a.sequence', 'ASC')->result(array('modelid' => $row->id));

				foreach ($fields as $key => $value) {
					if ($value->isstatement != 1) {
						if ($value->input_type != 'multiple') {
							$fields_post[$value->variablename] = getPost($value->variablename);
						} else {
							$stored_value = array();
							foreach (json_decode($value->other) as $k => $v) {
								$variable_name = $v->variable_name;
								$post_variable = getPost("$variable_name" . "[]", []);

								$stored_value[$variable_name] = $post_variable;
							}

							$fields_post[$value->variablename] = json_encode($stored_value);
						}
					} else {
						$statement = json_decode($value->statement);

						if ($statement->statement == 'gender') {
							if ($statement->type == 'equals') {
								if (($statement->with == 'Perempuan' && $warga->jenis_kelamin == $statement->with) || ($statement->with == 'Laki-laki' && ($warga->jenis_kelamin == 'Laki-laki' || $warga->jenis_kelamin == 'Laki - laki'))) {
									$fields_post[$value->variablename] = $statement->valid;
								} else {
									$fields_post[$value->variablename] = $statement->invalid;
								}
							}
						}
					}
				}
			}
		} else {
			$row = $model->result();

			$modelid = array();
			foreach ($row as $key => $value) {
				$modelid[] = $value->id;
			}

			$fields = $this->fieldsurat->order_by('a.sequence', 'ASC')->where_in(array(
				'modelid' => $modelid
			))->result();

			foreach ($fields as $key => $value) {
				if ($value->isstatement != 1) {
					if ($value->input_type != 'multiple') {
						$fields_post[$value->variablename] = getPost($value->variablename);
					} else {
						$stored_value = array();
						foreach (json_decode($value->other) as $k => $v) {
							$variable_name = $v->variable_name;
							$post_variable = getPost("$variable_name" . "[]", []);

							$stored_value[$variable_name] = $post_variable;
						}

						$fields_post[$value->variablename] = json_encode($stored_value);
					}
				} else {
					$statement = json_decode($value->statement);

					if ($statement->statement == 'gender') {
						if ($statement->type == 'equals') {
							if ($warga->jenis_kelamin == $statement->with) {
								$fields_post[$value->variablename] = $statement->valid;
							} else {
								$fields_post[$value->variablename] = $statement->invalid;
							}
						}
					}
				}
			}
		}

		$categoryletters = $this->kategorisurat->get(array('id' => $jenis_surat));

		if ($categoryletters->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Data kategori surat tidak ditemukan');
		}

		$insert = array();
		$insert['jenis_surat'] = $jenis_surat;
		$insert['nik'] = $nik;
		$insert['nomor_hp'] = $nomor_hp;
		$insert['tanggal_permohonan'] = getCurrentDate();
		$insert['penandatanganid'] = $d_penandatangan->row()->id;

		if (get_cookie('mode_surat') == null || get_cookie('mode_surat') == 'otomatis') {
			$insert['status'] = 'Menunggu';
			$insert['tanggal_status'] = getCurrentDate();
			$insert['tipe_surat'] = 'otomatis';

			if (getPost('nomor_surat') != null) {
				$insert['nomor_surat'] = getPost('nomor_surat');
			} else {
				$insert['nomor_surat'] = replaceFormatSurat($this->subdomain_account->id, getCurrentSettingUmum($this->subdomain_account->id)->format_surat, $modelsurat, getPost('nomor_surat'));
			}
		} else {
			if (getPost('nomor_surat') != null) {
				$insert['nomor_surat'] = getPost('nomor_surat');
			} else {
				$insert['nomor_surat'] = replaceFormatSurat($this->subdomain_account->id, getCurrentSettingUmum($this->subdomain_account->id)->format_surat, $modelsurat, getPost('nomor_surat'));
			}
			$insert['status'] = 'Selesai';
			$insert['tipe_surat'] = 'manual';
		}

		if (isset($row->id)) {
			$insert['modelid'] = $row->id;
		}

		if ($modelsurat == 'All') {
			$insert['modelselection'] = $modelsurat;
		}

		$insert['field_json'] = json_encode($fields_post);

		$this->surat->insert($insert);
		$id_surat = $this->db->insert_id();

		$link = base_url('surat/permohonan/baru/id/' . $id_surat);

		if ($id_surat) {
			$wablas = null;
			$whatsapp_status = array();
			if (get_cookie('mode_surat') == null || get_cookie('mode_surat') == 'otomatis') {
				$kontak = $this->detailkategorisurat->getDefaultData(array(
					'a.catsuratid' => $this->input->post('jenis_surat'),
					'a.id_user' => $this->subdomain_account->id,
					'a.id' => $d_penandatangan->row()->id
				));

				$message = "Ada permohonan surat baru dari warga anda. Silahkan cek dan lakukan approve dengan cara KLIK link berikut ini : $link";

				foreach ($kontak->result() as $key => $value) {
					$telepon = $value->nomor_handphone;
					$wablas = sendMessageWhatsapp($telepon, $message);

					$message_sms = "Ada permohonan surat baru dari warga anda.
NIK: " . $this->input->post('nik') . "
Nama : $warga->nama
Jenis Surat: $jenis_surat

Balas dengan mengetik OK.$id_surat untuk melakukan Approve";

					// $this->send_smsgateway($telepon, $message_sms);

					$whatsapp_status[] = $wablas;
				}
			}

			return JSONResponse(array(
				'RESULT' => 'OK',
				'MESSAGE' => 'Data berhasil disimpan',
				'WABLAS' => $wablas,
				'ID_SURAT' => $id_surat,
				'WHATSAPP_STATUS' => $whatsapp_status
			));
		} else {
			return JSONResponseDefault('FAILED', 'Gagal menambahkan data');
		}
	}

	public function done()
	{
		return $this->load->view('surat/done');
	}

	public function arsip_masuk()
	{
		if (!isLogin()) {
			return redirect(base_url('auth/login'));
		} else if (!isAdmin()) {
			return redirect(base_url('dashboard'));
		}

		$data = array();
		$data['title'] = 'Arsip Surat Masuk';
		$data['content'] = 'surat/arsip/masuk/index';
		$data['surat'] = $this->arsip_surat->result(array(
			'userid' => getCurrentIdUser()
		));

		return $this->load->view('master', $data);
	}

	public function add_arsip_masuk()
	{
		if (!isLogin()) {
			return redirect(base_url('auth/login'));
		} else if (!isAdmin()) {
			return redirect(base_url('dashboard'));
		}

		$data = array();
		$data['title'] = 'Tambah Arsip Surat Masuk';
		$data['content'] = 'surat/arsip/masuk/add';

		return $this->load->view('master', $data);
	}

	public function process_add_arsip_masuk()
	{
		if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Anda belum login');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
		}

		$incomingdate = getPost('incomingdate');
		$letternumber = getPost('letternumber');
		$nama = getPost('name');
		$senderaddress = getPost('senderaddress');
		$note = getPost('note');

		try {
			$upload = doUpload_CloudStorage('file', 'pdf|doc|docx');
			$file_name = $upload['name'];

			$data = array(
				'nama' => $nama,
				'dokumen' => $file_name,
				'note' => $note,
				'letternumber' => $letternumber,
				'senderaddress' => $senderaddress,
				'incomingdate' => $incomingdate,
				'userid' => getCurrentIdUser(),
				'createddate' => getCurrentDate(),
				'createdby' => getCurrentIdUser(),
			);

			$this->arsip_surat->insert($data);

			return JSONResponseDefault('OK', 'Data berhasil disimpan');
		} catch (Exception $ex) {
			return JSONResponseDefault('FAILED', $ex->getMessage());
		}
	}

	public function process_delete_arsip_masuk()
	{
		if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Anda belum login');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
		}

		$id = getPost('id');

		$get = $this->arsip_surat->get(array(
			'id' => $id,
			'userid' => getCurrentIdUser()
		));

		if ($get->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
		} else {
			$this->arsip_surat->delete(array(
				'id' => $id,
				'userid' => getCurrentIdUser()
			));

			return JSONResponseDefault('OK', 'Data berhasil dihapus');
		}
	}

	public function edit_arsip_masuk($id)
	{
		if (!isLogin()) {
			return redirect(base_url('auth/login'));
		} else if (!isAdmin()) {
			return redirect(base_url('dashboard'));
		}

		$get = $this->arsip_surat->get(array(
			'id' => $id,
			'userid' => getCurrentIdUser()
		));

		if ($get->num_rows() == 0) {
			return redirect(base_url('surat/arsip/masuk'));
		}

		$data = array();
		$data['title'] = 'Ubah Arsip Surat Masuk';
		$data['content'] = 'surat/arsip/masuk/edit';
		$data['row'] = $get->row();

		return $this->load->view('master', $data);
	}

	public function process_edit_arsip_masuk($id)
	{
		if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Anda belum login');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
		}

		$incomingdate = getPost('incomingdate');
		$letternumber = getPost('letternumber');
		$nama = getPost('name');
		$senderaddress = getPost('senderaddress');
		$note = getPost('note');
		$file = $_FILES['file'];

		$get = $this->arsip_surat->get(array(
			'id' => $id,
			'userid' => getCurrentIdUser()
		));

		if ($get->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
		} else {
			$row = $get->row();

			$data = array(
				'incomingdate' => $incomingdate,
				'letternumber' => $letternumber,
				'nama' => $nama,
				'senderaddress' => $senderaddress,
				'note' => $note,
				'updateddate' => getCurrentDate(),
				'updatedby' => getCurrentIdUser(),
			);

			if (!empty($file['name'])) {
				try {
					$upload = doUpload_CloudStorage('file', 'pdf|doc|docx');
					$file_name = $upload['name'];
					$data['dokumen'] = $file_name;
				} catch (Exception $ex) {
					return JSONResponseDefault('FAILED', $ex->getMessage());
				}
			}

			$this->arsip_surat->update(array(
				'id' => $id,
				'userid' => getCurrentIdUser()
			), $data);

			return JSONResponseDefault('OK', 'Data berhasil disimpan');
		}
	}

	public function permohonan_baru()
	{
		if (!isLogin()) {
			return redirect('auth/login');
		} else if (!isAdmin()) {
			return redirect('dashboard');
		}

		$data = array();
		$data['title'] = 'Arsip Surat Keluar';
		$data['content'] = 'surat/permohonan_baru';
		$data['surat'] = $this->surat->getDataSurat(array(
			'b.id_user' => getCurrentIdUser()
		))->result();

		return $this->load->view('master', $data);
	}

	public function api_approve()
	{
		$id = getPost('id');

		$get = $this->surat->getDefaultData(array(
			'a.id_surat' => $id
		));

		if ($get->num_rows() == 0) {
			return JSONResponse(array('result' => false, 'message' => 'data not found'));
		}

		$get_nama = $this->warga->getDefaultData(array('a.nik' => $get->row()->nik))->row()->nama;

		$update = array();
		$update['status'] = 'Selesai';
		$update['tanggal_status'] = getCurrentDate();

		$update = $this->surat->update(array('id_surat' => $id), $update);

		if ($update) {
			$link = base_url('surat/cetak/' . $id);
			sendMessageWhatsapp($get->row()->nomor_hp, "Surat anda sudah selesai diverifikasi, Download dan print dengan klik link berikut ini $link");

			$kontak = $this->detailkategorisurat->getDefaultData(array(
				'a.catsuratid' => $get->row()->jenis_surat,
				'a.id_user' => $this->subdomain_account->id
			));

			$message = "Surat berhasil dikonfirmasi dan telah dikirimkan kepada $get_nama";

			foreach ($kontak->result() as $key => $value) {
				$telepon = $value->nomor_handphone;
				$this->send_smsgateway($telepon, $message);
			}

			return JSONResponse(array('result' => true, 'message' => 'success to update data'));
		} else {
			return JSONResponse(array('result' => false, 'message' => 'failed to update data'));
		}
	}

	public function set_approve()
	{
		$id = getPost('id');
		$status = getPost('status');
		$nomorsurat = getPost('nomor');

		$get = $this->surat->getDefaultData(array(
			'a.id_surat' => $id
		));

		if ($get->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
		}

		$update = array();
		$update['status'] = $status;
		if ($status == 'Selesai' && $nomorsurat != null) {
			$update['nomor_surat'] = $nomorsurat;
		}
		$update['tanggal_status'] = getCurrentDate();

		$update = $this->surat->update(array('id_surat' => $id), $update);
		$link = base_url('surat/cetak/' . $id);

		if ($update) {
			if ($status == 'Selesai') {
				sendMessageWhatsapp($get->row()->nomor_hp, "Surat anda sudah selesai diverifikasi, Download dan print dengan klik link berikut ini $link");
			} else {
				sendMessageWhatsapp($get->row()->nomor_hp, "Permohonan Surat anda ditolak, silahkan hubungi admin untuk informasi lebih lanjut");
			}

			return JSONResponseDefault('OK', 'Data berhasil diubah');
		} else {
			return JSONResponseDefault('FAILED', 'Gagal mengubah data');
		}
	}

	public function permohonan($id)
	{
		if (!is_numeric($id)) {
			return redirect(base_url('admin'));
		}

		$get = $this->db->query("SELECT a.*, b.*, f.nama AS kategori_surat, c.nama_kabkota, d.nama_kecamatan, e.nama_kelurahan, a.status AS status_surat FROM surat a JOIN warga b ON b.nik = a.nik JOIN kabkota c ON c.id_kabkota = b.id_kabkota JOIN kecamatan d ON d.id_kecamatan = b.id_kecamatan JOIN kelurahan e ON e.id_kelurahan = b.id_kelurahan JOIN kategori_surat f ON f.id = a.jenis_surat WHERE a.id_surat = $id");

		if ($get->num_rows() == 0) {
			return redirect('admin');
		}

		$get_setting = $this->settingumum->getDefaultData(array('a.id_user' => $get->row()->id_user))->row();

		$data = array();
		$data['surat'] = $get->row();
		$data['logo'] = $get_setting->logo_desa;

		return $this->load->view('permohonan', $data);
	}

	public function preview($id)
	{
		$letteraccess = $this->letteraccess->get(array(
			'userid' => $this->subdomain_account->id,
			'categoryid' => $id
		));

		if ($letteraccess->num_rows() == 0) {
			return redirect('admin');
		}

		$model = $this->modelsurat->get(array(
			'categorylettersid' => $letteraccess->row()->categoryid
		));

		if ($model->num_rows() == 0) {
			return redirect('admin');
		}

		$rowmodel = $model->row();

		$pdf = new Html2Pdf('P', 'A4', 'fr', true, 'UTF-8', array(15, 15, 15, 15));
		$pdf->setTestTdInOnePage(false);

		if ($rowmodel->lettermethod != 'Upload') {
			$formatted = $model->row()->modelformat;
			$formatted = str_replace('https://desa.gides.id/uploads/51c085483340c0c286c08992fe65bb0e.png', 'https://asset.gides.id/51c085483340c0c286c08992fe65bb0e.png', $formatted);

			$pdf->writeHTML($this->load->view(
				'preview_surat',
				array(
					'formatted' => $formatted,
				),
				true
			));

			$pdf->output('Surat.pdf');
		} else {
			$convert = convertWordToPDF($rowmodel->letterformat);

			if ($convert && isset($convert->success)) {
				$filename = $rowmodel->letterformat;
				$filename = explode('.', $filename);
				$filename = $filename[0];

				return redirect(asset_url($filename . '.pdf'));
			} else {
				return redirect(asset_url($rowmodel->letterformat));
			}
		}
	}

	public function cetak($id)
	{
		if (!is_numeric($id)) {
			return redirect(base_url('admin'));
		}

		$get = $this->db->query("SELECT a.*, b.*, c.nama_kabkota, d.nama_kecamatan, e.nama_kelurahan, f.nama AS nama_penandatangan, f.jabatan AS jabatan_penandatangan, a.status AS status_surat FROM surat a JOIN warga b ON b.nik = a.nik JOIN kabkota c ON c.id_kabkota = b.id_kabkota JOIN kecamatan d ON d.id_kecamatan = b.id_kecamatan JOIN kelurahan e ON e.id_kelurahan = b.id_kelurahan LEFT JOIN detail_kategori_surat f ON f.id = a.penandatanganid WHERE a.id_surat = $id");

		if ($get->num_rows() == 0) {
			return redirect('admin');
		}

		$row = $get->row();

		$pdf = new Html2Pdf('P', 'A4', 'fr', true, 'UTF-8', array(15, 15, 15, 15));
		$pdf->setTestTdInOnePage(false);

		if ($row->modelselection != 'All') {
			$model = $this->modelsurat->get(array(
				'a.id' => $row->modelid
			));

			if ($model->num_rows() == 0) {
				return $this->_cetak($id);
			}

			$rowmodel = $model->row();

			if ($rowmodel->lettermethod != 'Upload') {
				$pdf->writeHTML($this->load->view('generate_surat', array(
					'surat' => $row,
					'model' => $rowmodel
				), true));

				$pdf->output('Surat.pdf');
			} else {
				$warga = $this->db->get_where('warga', array('nik' => $row->nik))->row();
				$keluarga = $this->db->get_where('warga', array('nomor_kk' => $warga->nomor_kk))->result();

				$ayah = null;
				$ibu = null;

				foreach ($keluarga as $key => $value) {
					if (($value->jenis_kelamin == 'Laki-laki' || $value->jenis_kelamin == 'Laki - laki') && in_array(strtolower($value->status_hubungan_dalam_keluarga), isAyah_Indicator())) {
						if ($ayah != null) continue;

						$ayah = $value;
					} else if ($value->jenis_kelamin == 'Perempuan' && in_array(strtolower($value->status_hubungan_dalam_keluarga), isIbu_Indicator())) {
						if ($ibu != null) continue;

						$ibu = $value;
					}
				}

				$kecamatan_warga = $this->db->get_where('kecamatan', array('id_kecamatan' => $warga->id_kecamatan))->row();
				$kabupaten_warga = $this->db->get_where('kabkota', array('id_kabkota' => $kecamatan_warga->id_kabkota))->row();
				$penandatangan = $this->db->get_where('detail_kategori_surat', array('id' => $row->penandatanganid))->row();
				$kelurahan = $this->db->get_where('kelurahan', array('id_kelurahan' => $warga->id_kelurahan))->row();
				$setting_umum = $this->db->get_where('setting_umum', array('id_user' => $warga->id_user))->row();

				// Validate letterformat is not empty
				if (empty($rowmodel->letterformat)) {
					error_log('Letter format is empty or null for model ID: ' . (isset($rowmodel->id) ? $rowmodel->id : 'unknown'));
					return redirect(base_url('admin') . '?error=empty_letter_format');
				}

				// Download file from remote URL with validation
				$remote_file_url = asset_url($rowmodel->letterformat);
				$local_file_path = './application/cache/' . $rowmodel->letterformat;

				// Validate paths are properly formed
				if (empty($remote_file_url) || $remote_file_url === false) {
					error_log('Invalid remote URL generated for letterformat: ' . $rowmodel->letterformat);
					return redirect(base_url('admin') . '?error=invalid_remote_url');
				}

				// Download the file
				$file_content = file_get_contents($remote_file_url);

				// Validate that the file was downloaded successfully
				if ($file_content === false) {
					error_log('Failed to download file from URL: ' . $remote_file_url);
					return redirect(base_url('admin') . '?error=file_download_failed');
				}

				// Log download success and check content type
				error_log('Successfully downloaded file from: ' . $remote_file_url . ' (Size: ' . strlen($file_content) . ' bytes)');

				// Check if the downloaded content looks like HTML (error page) instead of a Word document
				if (stripos($file_content, '<html') !== false || stripos($file_content, '<!doctype') !== false) {
					error_log('Downloaded content appears to be HTML instead of Word document: ' . substr($file_content, 0, 200));
					return redirect(base_url('admin') . '?error=file_is_html_not_word');
				}

				// Save the file
				$bytes_written = file_put_contents($local_file_path, $file_content);

				// Validate that the file was saved successfully
				if ($bytes_written === false || $bytes_written === 0) {
					return redirect(base_url('admin') . '?error=file_save_failed');
				}

				// Validate that the file exists and is readable
				if (!file_exists($local_file_path) || !is_readable($local_file_path)) {
					return redirect(base_url('admin') . '?error=file_not_accessible');
				}

				// Validate file size (should be greater than 0)
				if (filesize($local_file_path) === 0) {
					return redirect(base_url('admin') . '?error=empty_file');
				}

				// check if file is doc or docx
				$ext = pathinfo($rowmodel->letterformat, PATHINFO_EXTENSION);

				// Additional validation for Word files
				if ($ext == 'docx') {
					// DOCX files should be valid ZIP archives
					$zip = new ZipArchive();
					$zip_result = $zip->open($local_file_path);
					if ($zip_result !== TRUE) {
						error_log('Invalid DOCX file (not a valid ZIP): ' . $local_file_path . ' - ZIP Error Code: ' . $zip_result);
						return redirect(base_url('admin') . '?error=invalid_docx_format');
					}
					$zip->close();
				} elseif ($ext == 'doc') {
					// DOC files should have the correct binary signature
					$file_handle = fopen($local_file_path, 'rb');
					if ($file_handle) {
						$header = fread($file_handle, 8);
						fclose($file_handle);

						// Check for DOC file signature (OLE2 format)
						// DOC files start with D0CF11E0A1B11AE1 (OLE2 signature)
						$ole_signature = "\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1";
						if (substr($header, 0, 8) !== $ole_signature) {
							error_log('Invalid DOC file (incorrect binary signature): ' . $local_file_path . ' - Header: ' . bin2hex($header));
							return redirect(base_url('admin') . '?error=invalid_doc_format');
						}
					} else {
						error_log('Cannot read DOC file header: ' . $local_file_path);
						return redirect(base_url('admin') . '?error=cannot_read_doc_file');
					}
				}

				// Log the file details for debugging
				error_log('Processing Word file: ' . $local_file_path . ' (Size: ' . filesize($local_file_path) . ' bytes, Extension: ' . $ext . ')');

				// Pre-validate that PHPWord can actually process this file
				$validation_passed = false;
				try {
					if ($ext == 'doc') {
						// Test if PHPWord can read the DOC file
						$test_document = \PhpOffice\PhpWord\IOFactory::load($local_file_path);
						if ($test_document) {
							unset($test_document); // Free memory
							error_log('PHPWord pre-validation successful for DOC file: ' . $local_file_path);
							$validation_passed = true;
						}
					} else {
						// Test if TemplateProcessor can read the DOCX file
						$test_processor = new TemplateProcessor($local_file_path);
						if ($test_processor) {
							unset($test_processor); // Free memory
							error_log('PHPWord pre-validation successful for DOCX file: ' . $local_file_path);
							$validation_passed = true;
						}
					}
				} catch (Exception $e) {
					error_log('PHPWord pre-validation failed: ' . $e->getMessage() . ' - File: ' . $local_file_path);

					// Log additional file information for debugging
					if (file_exists($local_file_path)) {
						$file_size = filesize($local_file_path);
						$mime_type = function_exists('mime_content_type') ? mime_content_type($local_file_path) : 'unknown';
						error_log("File details - Size: $file_size bytes, MIME: $mime_type, Extension: $ext");

						// Check first few bytes
						$handle = fopen($local_file_path, 'rb');
						if ($handle) {
							$first_bytes = fread($handle, 20);
							fclose($handle);
							error_log('First 20 bytes (hex): ' . bin2hex($first_bytes));
						}
					}

					// Return with detailed error information
					return redirect(base_url('admin') . '?error=phpword_validation_failed&file=' . urlencode($rowmodel->letterformat) . '&details=' . urlencode($e->getMessage()));
				}

				// If pre-validation failed, don't proceed
				if (!$validation_passed) {
					error_log('PHPWord pre-validation failed - validation_passed is false for file: ' . $local_file_path);
					return redirect(base_url('admin') . '?error=phpword_validation_failed&file=' . urlencode($rowmodel->letterformat));
				}

				try {
					if ($ext == 'doc') {
						// Validate that the file can be loaded by PHPWord
						$document = \PhpOffice\PhpWord\IOFactory::load($local_file_path);

						$objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($document, 'Word2007');
						$objWriter->save($local_file_path . 'x');

						$templateprocessor = new TemplateProcessor($local_file_path . 'x');
					} else {
						// Validate that the file can be loaded as a template
						$templateprocessor = new TemplateProcessor($local_file_path);
					}
				} catch (Exception $e) {
					// Log the error for debugging with more details
					error_log('PHPWord Error: ' . $e->getMessage() . ' - File: ' . $local_file_path . ' - File exists: ' . (file_exists($local_file_path) ? 'yes' : 'no') . ' - File size: ' . filesize($local_file_path));

					// Try to get more information about the file
					if (file_exists($local_file_path)) {
						$file_info = pathinfo($local_file_path);
						$mime_type = mime_content_type($local_file_path);
						error_log('File info - Name: ' . $file_info['basename'] . ', MIME: ' . $mime_type);

						// Additional debugging: check file content
						$file_handle = fopen($local_file_path, 'rb');
						if ($file_handle) {
							$first_bytes = fread($file_handle, 50);
							fclose($file_handle);
							error_log('First 50 bytes (hex): ' . bin2hex($first_bytes));
						}
					}

					// Provide a more user-friendly error message with troubleshooting info
					$error_details = array(
						'message' => $e->getMessage(),
						'file' => $rowmodel->letterformat,
						'size' => file_exists($local_file_path) ? filesize($local_file_path) : 0,
						'url' => $remote_file_url
					);

					error_log('PHPWord processing failed with details: ' . json_encode($error_details));

					return redirect(base_url('admin') . '?error=invalid_word_document&file=' . urlencode($rowmodel->letterformat) . '&details=' . urlencode($e->getMessage()));
				}

				$templateprocessor->setValues(array(
					'${url_desa}' => strtolower(base_url()),
					'${kabupaten_desa}' => strtoupper($setting_umum->kabupaten ?? '-'),
					'${kecamatan_desa}' => strtoupper($setting_umum->kecamatan ?? '-'),
					'${nama_desa}' => strtoupper($setting_umum->desa ?? '-'),
					'${alamat_desa}' => strtoupper($setting_umum->alamat ?? '-') . " KODEPOS " . $setting_umum->kodepos,
					'${nomor_surat}' => $row->nomor_surat ? strtoupper($row->nomor_surat) : '_________________________',
					'${nama}' => strtoupper($warga->nama),
					'${nomor_kk}' => strtoupper($warga->nomor_kk),
					'${nik}' => strtoupper($warga->nik),
					'${jenis_kelamin}' => strtoupper($warga->jenis_kelamin),
					'${tempat_lahir}' => strtoupper($warga->tempat_lahir),
					'${tanggal_lahir}' => strtoupper(tgl_indo($warga->tanggal_lahir)),
					'${agama}' => strtoupper($warga->agama),
					'${status_perkawinan}' => strtoupper($warga->status_perkawinan),
					'${pekerjaan}' => strtoupper($warga->pekerjaan),
					'${alamat}' => strtoupper($warga->alamat),
					'${rt}' => strtoupper($warga->rt),
					'${rw}' => strtoupper($warga->rw),
					'${kecamatan}' => strtoupper($kecamatan_warga->nama_kecamatan),
					'${kabupaten}' => strtoupper($kabupaten_warga->nama_kabkota),
					'${pendidikan_terakhir}' => strtoupper($warga->pendidikan),
					'${tanggal_permohonan}' => strtoupper(tgl_indo($row->tanggal_permohonan)),
					'${nama_penandatangan}' => strtoupper($penandatangan->nama),
					'${jabatan_penandatangan}' => strtoupper($penandatangan->jabatan),
					'${alamat_penandatangan}' => strtoupper($penandatangan->address),
					'${keterangan_otomatis}' => $row->tipe_surat == 'otomatis' ? '*Dokumen ini telah ditanda tangani secara elektronik, menggunakan sertifikat elektronik' : null,
					'${pernyataan_anak}' => ($warga->jenis_kelamin == 'Laki-laki' || $warga->jenis_kelamin == 'Laki - laki') ? 'BIN' : 'BINTI',
				));

				if ($ayah != null) {
					$kecamatan_ayah = $this->db->get_where('kecamatan', array('id_kecamatan' => $ayah->id_kecamatan))->row();
					$kabupaten_ayah = $this->db->get_where('kabkota', array('id_kabkota' => $kecamatan_ayah->id_kabkota))->row();

					$templateprocessor->setValues(array(
						'${nama_ayah}' => strtoupper($ayah->nama),
						'${nik_ayah}' => strtoupper($ayah->nik),
						'${tempat_lahir_ayah}' => strtoupper($ayah->tempat_lahir),
						'${tanggal_lahir_ayah}' => strtoupper(tgl_indo($ayah->tanggal_lahir)),
						'${agama_ayah}' => strtoupper($ayah->agama),
						'${pekerjaan_ayah}' => strtoupper($ayah->pekerjaan),
						'${alamat_ayah}' => strtoupper($ayah->alamat),
						'${rt_ayah}' => strtoupper($ayah->rt),
						'${rw_ayah}' => strtoupper($ayah->rw),
						'${pendidikan_terakhir_ayah}' => strtoupper($ayah->pendidikan),
						'${kecamatan_ayah}' => strtoupper($kecamatan_ayah->nama_kecamatan),
						'${kabupaten_ayah}' => strtoupper($kabupaten_ayah->nama_kabkota),
					));
				} else {
					$templateprocessor->setValues(array(
						'${nama_ayah}' => '-',
						'${nik_ayah}' => '-',
						'${tempat_lahir_ayah}' => '-',
						'${tanggal_lahir_ayah}' => '-',
						'${agama_ayah}' => '-',
						'${pekerjaan_ayah}' => '-',
						'${alamat_ayah}' => '-',
						'${rt_ayah}' => '-',
						'${rw_ayah}' => '-',
						'${pendidikan_terakhir_ayah}' => '-',
						'${kecamatan_ayah}' => '-',
						'${kabupaten_ayah}' => '-',
					));
				}

				if ($ibu != null) {
					$kecamatan_ibu = $this->db->get_where('kecamatan', array('id_kecamatan' => $ibu->id_kecamatan))->row();
					$kabupaten_ibu = $this->db->get_where('kabkota', array('id_kabkota' => $kecamatan_ibu->id_kabkota))->row();

					$templateprocessor->setValues(array(
						'${nama_ibu}' => strtoupper($ibu->nama),
						'${nik_ibu}' => strtoupper($ibu->nik),
						'${tempat_lahir_ibu}' => strtoupper($ibu->tempat_lahir),
						'${tanggal_lahir_ibu}' => strtoupper(tgl_indo($ibu->tanggal_lahir)),
						'${agama_ibu}' => strtoupper($ibu->agama),
						'${pekerjaan_ibu}' => strtoupper($ibu->pekerjaan),
						'${alamat_ibu}' => strtoupper($ibu->alamat),
						'${rt_ibu}' => strtoupper($ibu->rt),
						'${rw_ibu}' => strtoupper($ibu->rw),
						'${pendidikan_terakhir_ibu}' => strtoupper($ibu->pendidikan),
						'${kecamatan_ibu}' => strtoupper($kecamatan_ibu->nama_kecamatan),
						'${kabupaten_ibu}' => strtoupper($kabupaten_ibu->nama_kabkota),
					));
				} else {
					$templateprocessor->setValues(array(
						'${nama_ibu}' => '',
						'${nik_ibu}' => '',
						'${tempat_lahir_ibu}' => '',
						'${tanggal_lahir_ibu}' => '',
						'${agama_ibu}' => '',
						'${pekerjaan_ibu}' => '',
						'${alamat_ibu}' => '',
						'${rt_ibu}' => '',
						'${rw_ibu}' => '',
						'${pendidikan_terakhir_ibu}' => '',
						'${kecamatan_ibu}' => '',
						'${kabupaten_ibu}' => '',
					));
				}

				if ($row->tipe_surat != 'otomatis') {
					$templateprocessor->setValue('${ttd_barcode}', '');
				} else {
					if ($row->sign == null) {
						$format = "Ditandatangani oleh " . strtoupper($penandatangan != null ? $penandatangan->nama : '') . " (" . strtoupper($penandatangan != null ? $penandatangan->jabatan : '') . ") pada tanggal " . DateFormat($row->tanggal_status, 'd F Y H:i:s');
						$qrcode = (new QRCode())->render($format);

						if (preg_match('/^data:image\/(\w+);base64,/', $qrcode, $type)) {
							$data = substr($qrcode, strpos($qrcode, ',') + 1);
							$type = strtolower($type[1]);

							if (in_array($type, ['jpg', 'jpeg', 'gif', 'png'])) {
								$data = str_replace(' ', '+', $data);
								$data = base64_decode($data);

								if ($data !== false) {
									file_put_contents("./application/cache/sign_$id." . $type, $data);

									$templateprocessor->setImageValue('ttd_barcode', array(
										'path' => './application/cache/sign_' . $id . '.' . $type,
										'ratio' => true,
										'width' => 300,
									));
								}
							}
						}
					} else {
						if (preg_match('/^data:image\/(\w+);base64,/', $row->sign, $type)) {
							$data = substr($row->sign, strpos($row->sign, ',') + 1);
							$type = strtolower($type[1]);

							if (in_array($type, ['jpg', 'jpeg', 'gif', 'png'])) {
								$data = str_replace(' ', '+', $data);
								$data = base64_decode($data);

								if ($data !== false) {
									file_put_contents("./application/cache/sign_$id." . $type, $data);

									$templateprocessor->setImageValue('ttd_barcode', array(
										'path' => './application/cache/sign_' . $id . '.' . $type,
										'ratio' => true,
										'width' => 100,
									));
								}
							}
						}
					}
				}

				// Only process logo if logo_desa is not empty
				if (!empty($setting_umum->logo_desa)) {
					// Extract filename from logo_desa path and create safe cache filename
					$logo_filename = basename($setting_umum->logo_desa);

					// Ensure we have a valid filename
					if (!empty($logo_filename)) {
						$cache_path = './application/cache/' . $logo_filename;

						// Download and cache the logo file
						$logo_content = file_get_contents(asset_url($setting_umum->logo_desa));
						if ($logo_content !== false) {
							file_put_contents($cache_path, $logo_content);

							// Set the image value only if file was successfully cached
							$templateprocessor->setImageValue('logo_desa', array(
								'path' => $cache_path,
								'ratio' => true,
								'width' => 100,
							));
						}
					}
				}

				if ($row->field_json != null) {
					$json = json_decode($row->field_json);

					foreach ($json as $key => $value) {
						$var = "$" . "{" . $key . "}";
						$value_decoded = json_decode($value);

						if (!is_object($value_decoded) || $value_decoded == null) {
							if ($value_decoded != null) {
								$templateprocessor->setValue($var, $value_decoded);
							} else {
								$templateprocessor->setValue($var, $value);
							}
						} else {
							$value_json = $value_decoded;

							$field = $this->db->get_where('field_surat', array(
								'variablename' => $key,
								'modelid' => $row->modelid
							))->row();

							if ($field != null) {
								$style = $field->style;

								if ($style == 'Table') {
									$table = new Table(array('borderSize' => 12, 'borderColor' => 'green', 'width' => 9000, 'unit' => TblWidth::TWIP));

									$saved_variable = array();

									$table->addRow();
									foreach (json_decode($field->other) as $k => $v) {
										$table->addCell(150)->addText($v->input_label, ['bold' => true]);

										$generate_variable = $v->variable_name;
										$$generate_variable = $value_json->$generate_variable;

										$saved_variable[] = $$generate_variable;
									}

									foreach ($saved_variable[0] as $kk => $vv) {
										$table->addRow();
										foreach ($saved_variable as $kkk => $vvv) {
											$table->addCell(150)->addText($vvv[$kk]);
										}
									}

									$templateprocessor->setComplexBlock($var, $table);
								} else if ($style == 'List Item (Ordered List)') {
									$text = new \PhpOffice\PhpWord\Element\TextRun();

									$first_variable = "";
									$saved_variable = array();
									foreach (json_decode($field->other) as $k => $v) {
										if (empty($first_variable)) $first_variable = $v->input_label;
										$generate_variable = $v->variable_name;
										$$generate_variable = $value_json->$generate_variable;

										$saved_variable[$v->input_label] = $$generate_variable;
									}

									$index = 0;
									foreach ($saved_variable[$first_variable] as $kk => $vv) {
										foreach ($saved_variable as $kkk => $vvv) {
											$text->addText(($index + 1) . '. ' . $kkk . ': ' . $vvv[$index]);
											$text->addTextBreak(1);
										}

										$index++;
									}

									$templateprocessor->setComplexBlock($var, $text);
								} else if ($style == 'List Item (Unordered List)') {
									$text = new \PhpOffice\PhpWord\Element\TextRun();

									$first_variable = "";
									$saved_variable = array();
									foreach (json_decode($field->other) as $k => $v) {
										if (empty($first_variable)) $first_variable = $v->input_label;
										$generate_variable = $v->variable_name;
										$$generate_variable = $value_json->$generate_variable;

										$saved_variable[$v->input_label] = $$generate_variable;
									}

									$list = "";

									$index = 0;
									foreach ($saved_variable[$first_variable] as $kk => $vv) {
										$list .= '<w:p><w:pPr><w:pStyle w:val="ListParagraph" /><w:numPr><w:ilvl w:val="0" /><w:numId w:val="1" /></w:numPr></w:pPr><w:r>';
										foreach ($saved_variable as $kkk => $vvv) {
											$list .= '<w:t>' . $kkk . ': ' . $vvv[$index] . '</w:t><w:br/>';
										}

										$list .= '</w:r></w:p>';
										$index++;
									}

									$text->addText(minifier($list));

									$templateprocessor->setComplexBlock($var, $text);
								}
							}
						}
					}
				}

				$templateprocessor->saveAs('./application/cache/' . 'SURAT_' . $row->id_surat . '.docx');

				try {
					$upload = doUpload_CloudStorage('./application/cache/' . 'SURAT_' . $row->id_surat . '.docx', 'docx', 'file');
					$filename = $upload['name'];
				} catch (Exception $ex) {
					return redirect(base_url('admin'));
				}

				$convert = convertWordToPDF($filename);

				if (isset($convert->success)) {
					$filename = explode('.', $filename);
					$filename = $filename[0];

					return redirect(asset_url($filename . '.pdf'));
				} else {
					return redirect(asset_url($filename));
				}
			}
		} else {
			$generate = $this->modelsurat->result(array(
				'categorylettersid' => $row->jenis_surat,
				"(lettermethod = 'Generate' OR lettermethod IS NULL) =" => true
			));

			$upload = $this->modelsurat->result(array(
				'categorylettersid' => $row->jenis_surat,
				'lettermethod' => 'Upload'
			));

			if (count($generate) > 0) {
				$pdf->writeHTML($this->load->view('generate_surat_all', array(
					'surat' => $row,
					'model' => $generate
				), true));

				$pdf->output('Surat.pdf');
			} else if (count($upload) > 0) {
				$zip = new clsTbsZip();
				$content = [];
				$r = '';

				foreach ($upload as $key => $value) {
					if ($key == 0) continue;

					// Download file with validation
					$remote_file_url = asset_url($value->letterformat);
					$local_file_path = './application/cache/' . $value->letterformat;

					$file_content = file_get_contents($remote_file_url);

					// Validate download
					if ($file_content === false) {
						error_log('Failed to download file: ' . $remote_file_url);
						continue; // Skip this file and continue with others
					}

					$bytes_written = file_put_contents($local_file_path, $file_content);

					// Validate file save
					if ($bytes_written === false || $bytes_written === 0 || !file_exists($local_file_path)) {
						error_log('Failed to save file: ' . $local_file_path);
						continue; // Skip this file and continue with others
					}

					try {
						$zip->Open($local_file_path);
						$content[$key] = $zip->FileRead('word/document.xml');
						$zip->Close();
					} catch (Exception $e) {
						error_log('Failed to process ZIP file: ' . $local_file_path . ' - Error: ' . $e->getMessage());
						continue; // Skip this file and continue with others
					}

					$p = strpos($content[$key], '<w:body');
					if ($p === false)
						echo ("Tag <w:body> not found in document ." . $value->letterformat);

					$p = strpos($content[$key], '>', $p);
					$content[$key] = substr($content[$key], $p + 1);

					$p = strpos($content[$key], '</w:body>');
					if ($p === false)
						echo ("Tag <w:body> not found in document ." . $value->letterformat);

					$content[$key] = substr($content[$key], 0, $p);
					$r .= '<w:br w:type="page"/>' . str_replace('<w:body>', '', $content[$key]);
				}

				// Download and validate the main file
				$main_remote_url = asset_url($upload[0]->letterformat);
				$main_local_path = './application/cache/' . $upload[0]->letterformat;

				$main_file_content = file_get_contents($main_remote_url);

				if ($main_file_content === false) {
					error_log('Failed to download main file: ' . $main_remote_url);
					return redirect('admin');
				}

				$main_bytes_written = file_put_contents($main_local_path, $main_file_content);

				if ($main_bytes_written === false || $main_bytes_written === 0 || !file_exists($main_local_path)) {
					error_log('Failed to save main file: ' . $main_local_path);
					return redirect('admin');
				}

				try {
					$zip->Open($main_local_path);
					$content2 = $zip->FileRead('word/document.xml');
				} catch (Exception $e) {
					error_log('Failed to process main ZIP file: ' . $main_local_path . ' - Error: ' . $e->getMessage());
					return redirect('admin');
				}

				$p = strpos($content2, '</w:body>');
				if ($p === false)
					echo ("Tag </w:body> not found in document ." . $upload[0]->letterformat);

				$content2 = substr_replace($content2, $r, $p, 0);
				$content2 = replace_template_surat($content2, $id);

				$zip->FileReplace('word/document.xml', $content2, TBSZIP_STRING);
				$zip->Flush(TBSZIP_FILE, './application/cache/' . $row->id_surat . '-MERGED' . '.docx');

				try {
					$upload = doUpload_CloudStorage('./application/cache/' . $row->id_surat . '-MERGED' . '.docx', 'docx', 'file');
					$filename = $upload['name'];
				} catch (Exception $ex) {
					return redirect('admin');
				}

				$convert = convertWordToPDF($filename);

				if (isset($convert->success)) {
					$filename = explode('.', $filename);
					$filename = $filename[0];
					return redirect(asset_url($filename . '.pdf'));
				} else {
					return redirect(asset_url($filename));
				}
			}
		}
	}

	public function _cetak($id)
	{
		if (!is_numeric($id)) {
			return redirect(base_url('admin'));
		}

		$print_surat = getGet('print_surat');

		$get = $this->db->query("SELECT a.*, b.*, c.nama_kabkota, d.nama_kecamatan, e.nama_kelurahan, f.nama AS nama_penandatangan, f.jabatan AS jabatan_penandatangan, a.status AS status_surat FROM surat a JOIN warga b ON b.nik = a.nik JOIN kabkota c ON c.id_kabkota = b.id_kabkota JOIN kecamatan d ON d.id_kecamatan = b.id_kecamatan JOIN kelurahan e ON e.id_kelurahan = b.id_kelurahan LEFT JOIN detail_kategori_surat f ON f.id = a.penandatanganid WHERE a.id_surat = $id");

		if ($get->num_rows() == 0) {
			return redirect('admin');
		}

		$get_setting = $this->settingumum->getDefaultData(array('a.id_user' => $get->row()->id_user))->row();

		$data = array();
		$data['surat'] = $get->row();
		$data['setting'] = $get_setting;
		$data['subdomain'] = $this->subdomain;

		if ($data['surat']->jenis_surat == 7) {
			$ayah = $this->warga->getDefaultData(array(
				'nik' => $data['surat']->nik_ayah
			))->row();

			if ($ayah == null) {
				$json_ayah = json_decode($data['surat']->json_ayah);
				$ayah = $json_ayah;
			}

			$ibu = $this->warga->getDefaultData(array(
				'nik' => $data['surat']->nik_ibu
			))->row();

			if ($ibu == null) {
				$json_ibu = json_decode($data['surat']->json_ibu);
				$ibu = $json_ibu;
			}

			$data['ayah'] = $ayah;
			$data['ibu'] = $ibu;
		}

		$kontak = $this->detailkategorisurat->getDefaultData(array(
			'a.catsuratid' => $get->row()->jenis_surat,
			'a.id_user' => $this->subdomain_account->id
		))->row();

		$data['kontak'] = $kontak;

		if ($print_surat != 1) {
			if ($data['surat']->jenis_surat == 8) {
				$html = $this->load->view('cetak_sk_domisili', $data, true);
			} else if ($data['surat']->jenis_surat == 9) {
				$html = $this->load->view('cetak_sk_penghasilan', $data, true);
			} else if ($data['surat']->jenis_surat == 10) {
				$html = $this->load->view('cetak_sk_skck', $data, true);
			} else if ($data['surat']->jenis_surat != 7) {
				$html = $this->load->view('cetak_surat', $data, true);
			} elseif ($data['surat']->jenis_surat == 7) {
				$surat = $data['surat'];

				if ($surat->tipe_surat_nikah == 'N1') {
					$html = $this->load->view('cetak_surat_nikah', $data, true);
				} else if ($surat->tipe_surat_nikah == 'N2') {
					$html = $this->load->view('cetak_surat_nikah_n2', $data, true);
				} else if ($surat->tipe_surat_nikah == 'N3') {
					$calon_sutri = $this->warga->getDefaultData(array(
						'nik' => $surat->nik_calon_sutri
					))->row();

					if ($calon_sutri == null) {
						$json_calon_sutri = json_decode($surat->json_calon_sutri);
						$calon_sutri = $json_calon_sutri;
					}

					$data['calon_sutri'] = $calon_sutri;

					$html = $this->load->view('cetak_surat_nikah_n3', $data, true);
				} else if ($surat->tipe_surat_nikah == 'N4') {
					$calon_sutri = $this->warga->getDefaultData(array(
						'nik' => $surat->nik_calon_sutri
					))->row();

					if ($calon_sutri == null) {
						$json_calon_sutri = json_decode($surat->json_calon_sutri);
						$calon_sutri = $json_calon_sutri;
					}

					$data['calon_sutri'] = $calon_sutri;

					$html = $this->load->view('cetak_surat_nikah_n4', $data, true);
				} else if ($surat->tipe_surat_nikah == 'N5') {
					$calon_sutri = $this->warga->getDefaultData(array(
						'nik' => $surat->nik_calon_sutri
					))->row();

					if ($calon_sutri == null) {
						$json_calon_sutri = json_decode($surat->json_calon_sutri);
						$calon_sutri = $json_calon_sutri;
					}

					$data['calon_sutri'] = $calon_sutri;

					$html = $this->load->view('cetak_surat_nikah_n5', $data, true);
				} else if ($surat->tipe_surat_nikah == 'N6') {
					$calon_sutri = $this->warga->getDefaultData(array(
						'nik' => $surat->nik_calon_sutri
					))->row();

					if ($calon_sutri == null) {
						$json_calon_sutri = json_decode($surat->json_calon_sutri);
						$calon_sutri = $json_calon_sutri;
					}

					$data['calon_sutri'] = $calon_sutri;

					$html = $this->load->view('cetak_surat_nikah_n6', $data, true);
				} else {
					$calon_sutri = $this->warga->getDefaultData(array(
						'nik' => $surat->nik_calon_sutri
					))->row();

					if ($calon_sutri == null) {
						$json_calon_sutri = json_decode($surat->json_calon_sutri);
						$calon_sutri = $json_calon_sutri;
					}

					$data['calon_sutri'] = $calon_sutri;

					$data['all'] = $data;
					$html = $this->load->view('cetak_surat_nikah_all', $data, true);
				}
			}

			$pdf = new Html2Pdf();
			$pdf->setTestTdInOnePage(false);
			$pdf->writeHTML($html);
			$pdf->output('Surat.pdf');
		} else {
			$data['print'] = true;

			if ($data['surat']->jenis_surat == 8) {
				return $this->load->view('cetak_sk_domisili', $data);
			} else if ($data['surat']->jenis_surat == 9) {
				return $this->load->view('cetak_sk_penghasilan', $data);
			} else if ($data['surat']->jenis_surat == 10) {
				return $this->load->view('cetak_sk_skck', $data);
			} else if ($data['surat']->jenis_surat != 7) {
				return $this->load->view('cetak_surat', $data);
			} elseif ($data['surat']->jenis_surat == 7) {
				$surat = $data['surat'];

				if ($surat->tipe_surat_nikah == 'N1') {
					return $this->load->view('cetak_surat_nikah', $data);
				} else if ($surat->tipe_surat_nikah == 'N2') {
					return $this->load->view('cetak_surat_nikah_n2', $data);
				} else if ($surat->tipe_surat_nikah == 'N3') {
					$calon_sutri = $this->warga->getDefaultData(array(
						'nik' => $surat->nik_calon_sutri
					))->row();

					if ($calon_sutri == null) {
						$json_calon_sutri = json_decode($surat->json_calon_sutri);
						$calon_sutri = $json_calon_sutri;
					}

					$data['calon_sutri'] = $calon_sutri;

					return $this->load->view('cetak_surat_nikah_n3', $data);
				} else if ($surat->tipe_surat_nikah == 'N4') {
					$calon_sutri = $this->warga->getDefaultData(array(
						'nik' => $surat->nik_calon_sutri
					))->row();

					if ($calon_sutri == null) {
						$json_calon_sutri = json_decode($surat->json_calon_sutri);
						$calon_sutri = $json_calon_sutri;
					}

					$data['calon_sutri'] = $calon_sutri;

					return $this->load->view('cetak_surat_nikah_n4', $data);
				} else if ($surat->tipe_surat_nikah == 'N5') {
					$calon_sutri = $this->warga->getDefaultData(array(
						'nik' => $surat->nik_calon_sutri
					))->row();

					if ($calon_sutri == null) {
						$json_calon_sutri = json_decode($surat->json_calon_sutri);
						$calon_sutri = $json_calon_sutri;
					}

					$data['calon_sutri'] = $calon_sutri;

					return $this->load->view('cetak_surat_nikah_n5', $data);
				} else if ($surat->tipe_surat_nikah == 'N6') {
					$calon_sutri = $this->warga->getDefaultData(array(
						'nik' => $surat->nik_calon_sutri
					))->row();

					if ($calon_sutri == null) {
						$json_calon_sutri = json_decode($surat->json_calon_sutri);
						$calon_sutri = $json_calon_sutri;
					}

					$data['calon_sutri'] = $calon_sutri;

					return $this->load->view('cetak_surat_nikah_n6', $data);
				} else {
					$calon_sutri = $this->warga->getDefaultData(array(
						'nik' => $surat->nik_calon_sutri
					))->row();

					if ($calon_sutri == null) {
						$json_calon_sutri = json_decode($surat->json_calon_sutri);
						$calon_sutri = $json_calon_sutri;
					}

					$data['calon_sutri'] = $calon_sutri;

					return $this->load->view('cetak_surat_nikah_all', $data);
				}
			}
		}
	}

	public function switch_mode()
	{
		if (get_cookie('mode_surat') == null) {
			set_cookie('mode_surat', 'manual', 0);
		} else {
			if (get_cookie('mode_surat') == 'manual') {
				set_cookie('mode_surat', 'otomatis', 0);
			} else {
				set_cookie('mode_surat', 'manual', 0);
			}
		}

		return redirect(base_url('surat'));
	}

	public function permohonan_baru_detail()
	{
		$nik = getPost('nik');

		$surat = $this->surat->select('a.*, b.nama AS nama_surat')
			->join('kategori_surat b', 'b.id = a.jenis_surat')
			->order_by('createddate', 'DESC')
			->result(array(
				'a.nik' => $nik
			));

		return JSONResponse(array(
			'RESULT' => 'OK',
			'CONTENT' => $this->load->view('surat/detail_permohonan_baru', array(
				'surat' => $surat,
				'nik' => $nik
			), true)
		));
	}

	public function datatables_permohonan_baru()
	{
		if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Anda belum login');
		}

		$datatables = $this->datatables->make('Surat_Model', 'QueryDatatables', 'SearchDatatables');

		$nik = getPost('nik');

		$where = array(
			'a.nik' => $nik
		);

		$data = array();
		foreach ($datatables->getData($where) as $key => $value) {
			if ($value->status == 'Menunggu') {
				$actions = "<button type=\"button\" class=\"btn btn-success btn-sm\" onclick=\"set_approve($value->id_surat, 'Selesai', '$value->nomor_surat')\">
					<i class=\"fa fa-check\"></i>
				</button>

				<button type=\"button\" class=\"btn btn-danger btn-sm mr-1\" onclick=\"set_approve($value->id_surat, 'Ditolak', '$value->nomor_surat')\">
					<i class=\"fa fa-ban\"></i>
				</button>";
			} else {
				if ($value->is_manual != 1) {
					$actions = "<a href=\"" . base_url('surat/cetak/' . $value->id_surat) . "\" class=\"btn btn-danger btn-sm mr-1\" target=\"_blank\">
						<i class=\"fa fa-file-pdf\"></i>
					</a>";
				} else {
					$actions = "<a href=\"" . asset_url($value->document) . "\" class=\"btn btn-danger btn-sm mr-1\" target=\"_blank\">
						<i class=\"fa fa-file-pdf\"></i>
					</a>";
				}
			}

			$actions .= "<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"delete_surat($value->id_surat)\">
				<i class=\"fa fa-trash\"></i>
			</button>";

			$detail = array();
			$detail[] = DateFormat($value->tanggal_permohonan, 'd F Y');
			if ($value->is_manual != 1) {
				$detail[] = $value->nama_surat;
			} else {
				$detail[] = "- Surat Manual -";
			}
			$detail[] = $value->keperluan;
			$detail[] = $value->status;
			$detail[] = $actions;

			$data[] = $detail;
		}

		return $datatables->json($data);
	}

	public function api_sync()
	{
		try {
			$this->db->trans_begin();

			$json = getPost('json');

			$data = json_decode($json);

			foreach ($data as $key => $value) {
				$insert = array();
				$insert['jenis_surat'] = $value->jenis_surat;
				$insert['nik'] = $value->nik;
				$insert['keperluan'] = $value->keperluan;
				$insert['tanggal_permohonan'] = $value->tanggal_permohonan;
				$insert['status'] = 'Selesai';
				$insert['tanggal_status'] = getCurrentDate();
				$insert['nomor_surat'] = $value->nomor_surat;

				if (isset($value->penandatangan)) {
					$insert['penandatanganid'] = $value->penandatangan;
				}

				$this->surat->insert($insert);
			}

			if ($this->db->trans_status() === FALSE) {
				$this->db->trans_rollback();

				return JSONResponseDefault('FAILED', 'Gagal melakukan sinkronisasi surat');
			} else {
				$this->db->trans_commit();

				return JSONResponseDefault('OK', 'Surat berhasil disinkronisasi');
			}
		} catch (Exception $ex) {
			$this->db->trans_rollback();

			return JSONResponseDefault('FAILED', 'Terjadi kesalahan saat melakukan sinkronisasi surat');
		}
	}

	public function cetak_nikah()
	{
		$pdf = new Html2Pdf();
		$pdf->setTestTdInOnePage(false);
		$pdf->writeHTML($this->load->view('cetak_surat_nikah', array(), true));
		$pdf->output("Surat.pdf");
	}

	public function cetak_nikah_n2()
	{
		$pdf = new Html2Pdf();
		$pdf->writeHTML($this->load->view('cetak_surat_nikah_n2', array(), true));
		$pdf->output("Surat.pdf");
	}

	public function cetak_nikah_n3()
	{
		$pdf = new Html2Pdf();
		$pdf->writeHTML($this->load->view('cetak_surat_nikah_n3', array(), true));
		$pdf->output("Surat.pdf");
	}

	public function cetak_wali_nikah()
	{
		$pdf = new Html2Pdf();
		$pdf->writeHTML($this->load->view('cetak_surat_nikah_perwali_nikah', array(), true));
		$pdf->output("Surat.pdf");
	}

	public function cetak_nikah_n4()
	{
		$pdf = new Html2Pdf();
		$pdf->writeHTML($this->load->view('cetak_surat_nikah_n4', array(), true));
		$pdf->output("Surat.pdf");
	}

	public function cetak_nikah_n5()
	{
		$pdf = new Html2Pdf();
		$pdf->writeHTML($this->load->view('cetak_surat_nikah_n5', array(), true));
		$pdf->output("Surat.pdf");
	}

	public function cetak_nikah_n6()
	{
		$pdf = new Html2Pdf();
		$pdf->writeHTML($this->load->view('cetak_surat_nikah_n6', array(), true));
		$pdf->output("Surat.pdf");
	}
	public function cetak_calon()
	{
		$pdf = new Html2Pdf();
		$pdf->writeHTML($this->load->view('cetak_surat_calon', array(), true));
		$pdf->output("Surat.pdf");
	}
	public function cetak_catin()
	{
		$pdf = new Html2Pdf();
		$pdf->writeHTML($this->load->view('cetak_surat_catin', array(), true));
		$pdf->output("Surat.pdf");
	}

	public function cetak_belum_nikah()
	{
		$pdf = new Html2Pdf();
		$pdf->writeHTML($this->load->view('cetak_surat_belum_nikah', array(), true));
		$pdf->output("Surat.pdf");
	}

	public function cetak_sk_penghasilan()
	{
		$pdf = new Html2Pdf();
		$pdf->setTestTdInOnePage(false);
		$pdf->writeHTML($this->load->view('cetak_sk_penghasilan', array(), true));
		$pdf->output("Surat.pdf");
	}

	public function cetak_sk_skck()
	{
		$pdf = new Html2Pdf();
		$pdf->setTestTdInOnePage(false);
		$pdf->writeHTML($this->load->view('cetak_sk_skck', array(), true));
		$pdf->output("Surat.pdf");
	}

	public function cetak_sk_domisili()
	{
		$pdf = new Html2Pdf();
		$pdf->setTestTdInOnePage(false);
		$pdf->writeHTML($this->load->view('cetak_sk_domisili', array(), true));
		$pdf->output("Surat.pdf");
	}

	public function delete()
	{
		$id = getPost('id');

		$this->surat->delete(array('id_surat' => $id));

		return JSONResponseDefault('OK', 'Surat berhasil dihapus');
	}

	public function check_nik()
	{
		$nik = getPost('nik');
		$types = getPost('types');

		$cek = $this->warga->getDefaultData(array(
			"(a.nik = '$nik' OR a.rfid = '$nik') =" => true,
		))->row();

		$data = array();
		$data['warga'] = $cek;
		$data['types'] = $types;

		return JSONResponse(array(
			'RESULT' => 'OK',
			'NAMA' => $cek != null ? $cek->nama : null,
			'CONTENT' => $this->load->view('surat/check_nik', $data, true)
		));
	}

	public function approve_digital()
	{
		$id = getPost('id');

		$get = $this->surat->get(array(
			'id_surat' => $id,
			'tipe_surat' => 'otomatis',
			'status' => 'Menunggu'
		));

		if ($get->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Surat tidak ditemukan');
		}

		$cek = $get->row();

		return JSONResponse(array(
			'RESULT' => 'OK',
			'CONTENT' => $this->load->view('surat/approve_digital', array(
				'id' => $id,
				'surat' => $cek
			), true)
		));
	}

	public function approve_digital_sign()
	{
		$id = getPost('id');
		$data = getPost('data');

		$get = $this->surat->get(array(
			'id_surat' => $id,
			'tipe_surat' => 'otomatis',
			'status' => 'Menunggu'
		));

		if ($get->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Surat tidak ditemukan');
		}

		$update = array();
		$update['status'] = 'Selesai';
		$update['sign'] = $data;
		$update['tanggal_status'] = getCurrentDate();

		$update = $this->surat->update(array('id_surat' => $id), $update);
		$link = base_url('surat/cetak/' . $id);

		if ($update) {
			sendMessageWhatsapp($get->row()->nomor_hp, "Surat anda sudah selesai diverifikasi, Download dan print dengan klik link berikut ini $link");

			return JSONResponseDefault('OK', 'Surat berhasil ditandatangani');
		} else {
			return JSONResponseDefault('FAILED', 'Gagal mendandatangani surat');
		}
	}

	public function add_arsip_keluar()
	{
		if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Anda belum login');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
		}

		return JSONResponse(array(
			'RESULT' => 'OK',
			'CONTENT' => $this->load->view('surat/add_arsip_keluar', array(), true)
		));
	}

	public function process_add_arsip_keluar()
	{
		if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Anda belum login');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
		}

		$nik = getPost('nik');
		$date = getPost('date');
		$description = getPost('description');
		$document = $_FILES['document'];

		$cek = $this->warga->getDefaultData(array(
			'nik' => $nik,
			'id_user' => getCurrentIdUser()
		))->row();

		if ($cek == null) {
			return JSONResponseDefault('FAILED', 'NIK tidak ditemukan');
		}

		try {
			$upload = doUpload_CloudStorage('document', 'pdf|doc|docx');
		} catch (Exception $ex) {
			return JSONResponseDefault('FAILED', $ex->getMessage());
		}

		$insert = array();
		$insert['nik'] = $nik;
		$insert['keperluan'] = $description;
		$insert['tanggal_permohonan'] = $date;
		$insert['status'] = 'Selesai';
		$insert['tanggal_status'] = getCurrentDate();
		$insert['is_manual'] = 1;
		$insert['document'] = $upload['name'];

		$this->surat->insert($insert);

		return JSONResponseDefault('OK', 'Surat berhasil ditambahkan');
	}
}
