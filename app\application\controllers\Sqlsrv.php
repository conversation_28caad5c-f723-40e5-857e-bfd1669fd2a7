<?php
// memory limit
ini_set('memory_limit', '2048M');
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property CI_DB_query_builder $db
 */
class Sqlsrv extends MY_Controller
{
    public function tables()
    {
        $sqlsrv = $this->load->database('sqlsrv', true);
        $query = $sqlsrv->query('SELECT * FROM siskeudes_tapin.sys.tables');

        $tables = [];
        foreach ($query->result() as $row) {
            $tables[] = $row->name;
        }

        return JSONResponse(array(
            'success' => true,
            'data' => $tables,
        ));
    }

    public function columns($tables)
    {
        $sqlsrv = $this->load->database('sqlsrv', true);
        $query = $sqlsrv->query("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '$tables'");

        $columns = [];
        foreach ($query->result() as $row) {
            $columns[] = $row->COLUMN_NAME;
        }

        return JSONResponse(array(
            'success' => true,
            'data' => $columns
        ));
    }

    public function data($tables, $page = 1, $limit = 10)
    {
        $sqlsrv = $this->load->database('sqlsrv', true);
        $query_sqlserver = "SELECT *, 0 AS _NAV_ORDER_F_ FROM $tables ORDER BY _NAV_ORDER_F_ OFFSET " . ($page - 1) * $limit . " ROWS FETCH NEXT $limit ROWS ONLY";
        $query = $sqlsrv->query($query_sqlserver);

        return JSONResponse(array(
            'success' => true,
            'data' => $query->result(),
            'total' => $sqlsrv->query("SELECT COUNT(*) AS total FROM $tables")->row()->total,
        ));
    }
}
