<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>

<!-- Hero Section -->
<section class="relative h-96 bg-cover bg-center"
    style="background-image: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');">
    <div class="absolute inset-0 flex items-center justify-center">
        <div class="text-center text-white max-w-4xl mx-auto px-4">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                Infografis Desa
            </h1>
            <p class="text-xl md:text-2xl font-light">
                Data dan Statistik <?= isset($setting->desa) ? $setting->desa : 'Desa' ?>
            </p>
        </div>
    </div>
</section>

<!-- Main Statistics -->
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <p class="text-primary font-medium mb-2">Data Kependudukan</p>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-800">Statistik Penduduk</h2>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            <!-- Total Population -->
            <div class="bg-gradient-to-br from-blue-500 to-blue-600 p-8 rounded-2xl text-white text-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-users text-2xl"></i>
                </div>
                <div class="text-3xl font-bold mb-2">
                    <?= isset($infografis->laki) && isset($infografis->perempuan) && $infografis->laki !== null && $infografis->perempuan !== null ? number_format($infografis->laki + $infografis->perempuan) : '0' ?>
                </div>
                <div class="text-white/80">Total Penduduk</div>
            </div>

            <!-- Male Population -->
            <div class="bg-gradient-to-br from-green-500 to-green-600 p-8 rounded-2xl text-white text-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-male text-2xl"></i>
                </div>
                <div class="text-3xl font-bold mb-2">
                    <?= isset($infografis->laki) && $infografis->laki !== null ? number_format($infografis->laki) : '0' ?>
                </div>
                <div class="text-white/80">Laki-laki</div>
            </div>

            <!-- Female Population -->
            <div class="bg-gradient-to-br from-pink-500 to-pink-600 p-8 rounded-2xl text-white text-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-female text-2xl"></i>
                </div>
                <div class="text-3xl font-bold mb-2">
                    <?= isset($infografis->perempuan) && $infografis->perempuan !== null ? number_format($infografis->perempuan) : '0' ?>
                </div>
                <div class="text-white/80">Perempuan</div>
            </div>

            <!-- Area -->
            <div class="bg-gradient-to-br from-orange-500 to-orange-600 p-8 rounded-2xl text-white text-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-map text-2xl"></i>
                </div>
                <div class="text-3xl font-bold mb-2">
                    <?= isset($luas_tanah->luas_tanah) && $luas_tanah->luas_tanah !== null ? number_format($luas_tanah->luas_tanah) : '0' ?>
                </div>
                <div class="text-white/80">Luas Wilayah (m²)</div>
            </div>
        </div>

        <!-- Gender Chart -->
        <div class="bg-gray-50 rounded-2xl p-8">
            <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">Perbandingan Jenis Kelamin</h3>
            <div class="max-w-md mx-auto">
                <canvas id="genderChart" width="400" height="400"></canvas>
            </div>
        </div>
    </div>
</section>

<!-- Age Demographics -->
<?php if (isset($infografis_umur) && count($infografis_umur) > 0) : ?>
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <p class="text-primary font-medium mb-2">Demografi</p>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800">Distribusi Usia</h2>
            </div>

            <div class="bg-white rounded-2xl p-8 shadow-lg">
                <canvas id="ageChart" width="800" height="400"></canvas>
            </div>

            <!-- Age Statistics Cards -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mt-8">
                <?php foreach (array_slice($infografis_umur, 0, 4) as $umur) : ?>
                    <div class="bg-white p-6 rounded-xl shadow-md text-center">
                        <div class="text-2xl font-bold text-primary mb-2">
                            <?= isset($umur->jumlah) && $umur->jumlah !== null ? number_format($umur->jumlah) : '0' ?>
                        </div>
                        <div class="text-gray-600 text-sm">
                            <?= $umur->nama ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
<?php endif; ?>

<!-- Education Statistics -->
<?php if (isset($infografis_pendidikan) && count($infografis_pendidikan) > 0) : ?>
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <p class="text-primary font-medium mb-2">Pendidikan</p>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800">Tingkat Pendidikan</h2>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <!-- Chart -->
                <div class="bg-gray-50 rounded-2xl p-8">
                    <canvas id="educationChart" width="400" height="400"></canvas>
                </div>

                <!-- Education List -->
                <div class="space-y-4">
                    <?php foreach ($infografis_pendidikan as $pendidikan) : ?>
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-primary rounded-full mr-3"></div>
                                <span class="font-medium text-gray-800"><?= $pendidikan->nama ?></span>
                            </div>
                            <span class="text-2xl font-bold text-primary"><?= isset($pendidikan->jumlah) && $pendidikan->jumlah !== null ? number_format($pendidikan->jumlah) : '0' ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </section>
<?php endif; ?>

<!-- Occupation Statistics -->
<?php if (isset($infografis_pekerjaan) && count($infografis_pekerjaan) > 0) : ?>
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <p class="text-primary font-medium mb-2">Ekonomi</p>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800">Jenis Pekerjaan</h2>
            </div>

            <div class="bg-white rounded-2xl p-8 shadow-lg">
                <canvas id="occupationChart" width="800" height="400"></canvas>
            </div>

            <!-- Top Occupations -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                <?php foreach (array_slice($infografis_pekerjaan, 0, 3) as $index => $pekerjaan) : ?>
                    <div class="bg-white p-6 rounded-xl shadow-md text-center">
                        <div class="w-12 h-12 bg-primary rounded-full mx-auto mb-4 flex items-center justify-center text-white font-bold text-lg">
                            <?= $index + 1 ?>
                        </div>
                        <h3 class="font-semibold text-gray-800 mb-2"><?= $pekerjaan->nama ?></h3>
                        <div class="text-2xl font-bold text-primary"><?= isset($pekerjaan->jumlah) && $pekerjaan->jumlah !== null ? number_format($pekerjaan->jumlah) : '0' ?></div>
                        <div class="text-gray-600 text-sm">orang</div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
<?php endif; ?>

<!-- Additional Information -->
<section class="py-16 bg-primary text-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">Informasi Tambahan</h2>
            <p class="text-xl text-white/80">Data dan layanan lainnya</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Government -->
            <a href="<?= base_url('pemerintah') ?>"
                class="group bg-white bg-opacity-10 p-8 rounded-2xl text-center hover:bg-opacity-20 transition-all duration-300">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-users text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">Pemerintahan</h3>
                <p class="text-white/80">Struktur dan perangkat desa</p>
            </a>

            <!-- News -->
            <a href="<?= base_url('berita/all') ?>"
                class="group bg-white bg-opacity-10 p-8 rounded-2xl text-center hover:bg-opacity-20 transition-all duration-300">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-newspaper text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">Berita</h3>
                <p class="text-white/80">Informasi terkini desa</p>
            </a>

            <!-- Budget -->
            <a href="<?= base_url('pengelolaandana') ?>"
                class="group bg-white bg-opacity-10 p-8 rounded-2xl text-center hover:bg-opacity-20 transition-all duration-300">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-chart-pie text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">APB Desa</h3>
                <p class="text-white/80">Transparansi anggaran</p>
            </a>
        </div>
    </div>
</section>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Gender Chart
    const genderCtx = document.getElementById('genderChart').getContext('2d');
    const genderChart = new Chart(genderCtx, {
        type: 'doughnut',
        data: {
            labels: ['Laki-laki', 'Perempuan'],
            datasets: [{
                data: [
                    <?= isset($infografis->laki) ? $infografis->laki : 0 ?>,
                    <?= isset($infografis->perempuan) ? $infografis->perempuan : 0 ?>
                ],
                backgroundColor: ['#3B82F6', '#EC4899'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        font: {
                            size: 14
                        }
                    }
                }
            }
        }
    });

    <?php if (isset($infografis_umur) && count($infografis_umur) > 0) : ?>
        // Age Chart
        const ageCtx = document.getElementById('ageChart').getContext('2d');
        const ageChart = new Chart(ageCtx, {
            type: 'bar',
            data: {
                labels: [<?php foreach ($infografis_umur as $umur) echo "'" . $umur->nama . "',"; ?>],
                datasets: [{
                    label: 'Jumlah Penduduk',
                    data: [<?php foreach ($infografis_umur as $umur) echo $umur->jumlah . ","; ?>],
                    backgroundColor: '#6e9e28',
                    borderRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f3f4f6'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    <?php endif; ?>

    <?php if (isset($infografis_pendidikan) && count($infografis_pendidikan) > 0) : ?>
        // Education Chart
        const educationCtx = document.getElementById('educationChart').getContext('2d');
        const educationChart = new Chart(educationCtx, {
            type: 'pie',
            data: {
                labels: [<?php foreach ($infografis_pendidikan as $pendidikan) echo "'" . $pendidikan->nama . "',"; ?>],
                datasets: [{
                    data: [<?php foreach ($infografis_pendidikan as $pendidikan) echo $pendidikan->jumlah . ","; ?>],
                    backgroundColor: [
                        '#6e9e28', '#8bb83a', '#5a7f20', '#9ca3af', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            font: {
                                size: 12
                            }
                        }
                    }
                }
            }
        });
    <?php endif; ?>

    <?php if (isset($infografis_pekerjaan) && count($infografis_pekerjaan) > 0) : ?>
        // Occupation Chart
        const occupationCtx = document.getElementById('occupationChart').getContext('2d');
        const occupationChart = new Chart(occupationCtx, {
            type: 'horizontalBar',
            data: {
                labels: [<?php foreach ($infografis_pekerjaan as $pekerjaan) echo "'" . $pekerjaan->nama . "',"; ?>],
                datasets: [{
                    label: 'Jumlah Pekerja',
                    data: [<?php foreach ($infografis_pekerjaan as $pekerjaan) echo $pekerjaan->jumlah . ","; ?>],
                    backgroundColor: '#6e9e28',
                    borderRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            color: '#f3f4f6'
                        }
                    },
                    y: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    <?php endif; ?>
</script>