<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Operator_Desa $operator_desa
 * @property MsMenu $msmenu
 * @property CI_DB_query_builder|CI_DB_mysqli_driver $db
 * @property AccessPermission $accesspermission
 */
class OperatorDesa extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Operator_Desa', 'operator_desa');
        $this->load->model('MsMenu', 'msmenu');
        $this->load->model('AccessPermission', 'accesspermission');
    }

    public function index()
    {
        $data = array();
        $data['title'] = 'Operator Desa';
        $data['content'] = 'operator_desa/index';
        $data['operatordesa'] = $this->operator_desa->result(array(
            'a.createdby' => getCurrentIdUser()
        ));

        return $this->load->view('master', $data);
    }

    public function add()
    {
        $data = array();
        $data['title'] = 'Tambah Operator Desa';
        $data['content'] = 'operator_desa/add';
        $data['menu'] = $this->msmenu->order_by('a.sequence', 'ASC')->result();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        $name = getPost('name');
        $username = getPost('username');
        $password = getPost('password');
        $phonenumber = getPost('phonenumber');
        $menuid = getPost('menuid', []);
        $submenuid = getPost('submenuid', []);

        $validate = $this->operator_desa->get(array(
            'a.createdby' => getCurrentIdUser(),
            'a.username' => $username
        ));

        if ($validate->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Username sudah digunakan');
        }

        $insert = array();
        $insert['name'] = $name;
        $insert['username'] = $username;
        $insert['phonenumber'] = $phonenumber;
        $insert['password'] = password_hash($password, PASSWORD_DEFAULT);
        $insert['createdby'] = getCurrentIdUser();

        $this->operator_desa->insert($insert);
        $id = $this->db->insert_id();

        foreach ($menuid as $key => $value) {
            $insert = array();
            $insert['userid'] = $id;
            $insert['menuid'] = $value;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->accesspermission->insert($insert);
        }

        foreach ($submenuid as $key => $value) {
            $insert = array();
            $insert['userid'] = $id;
            $insert['submenuid'] = $value;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->accesspermission->insert($insert);
        }

        return JSONResponseDefault('OK', 'Data berhasil disimpan');
    }

    public function edit($id)
    {
        $get = $this->operator_desa->get(array(
            'a.id' => $id,
            'a.createdby' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect('operator_desa');
        }

        $data = array();
        $data['title'] = 'Edit Operator Desa';
        $data['content'] = 'operator_desa/edit';
        $data['operatordesa'] = $get->row();
        $data['menu'] = $this->msmenu->order_by('a.sequence', 'ASC')->result();
        $data['accesspermission'] = $this->accesspermission->get(array('userid' => $id))->result();

        // get menuid from accesspermission and store it in $data['menuid']
        $data['menuid'] = array();
        foreach ($data['accesspermission'] as $key => $value) {
            if (!empty($value->menuid)) {
                $data['menuid'][] = $value->menuid;
            }
        }

        // get submenuid from accesspermission and store it in $data['submenuid']
        $data['submenuid'] = array();
        foreach ($data['accesspermission'] as $key => $value) {
            if (!empty($value->submenuid)) {
                $data['submenuid'][] = $value->submenuid;
            }
        }

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        $name = getPost('name');
        $username = getPost('username');
        $password = getPost('password');
        $phonenumber = getPost('phonenumber');
        $menuid = getPost('menuid', []);
        $submenuid = getPost('submenuid', []);

        $validate = $this->operator_desa->get(array(
            'a.createdby' => getCurrentIdUser(),
            'a.username' => $username,
            'a.id !=' => $id
        ));

        if ($validate->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Username sudah digunakan');
        }

        $update = array();
        $update['name'] = $name;
        $update['username'] = $username;
        $update['phonenumber'] = $phonenumber;

        if (!empty($password)) {
            $update['password'] = password_hash($password, PASSWORD_DEFAULT);
        }

        $this->operator_desa->update(array('id' => $id), $update);

        // check from accesspermission where userid = $id, jika data yang ada di accesspermission (menuid) tidak ada pada $menuid, maka hapus data tersebut, dan jika data $menuid tidak ada di accesspermission, maka insert data tersebut
        $accesspermission = $this->accesspermission->get(array('userid' => $id, 'menuid !=' => null))->result();
        foreach ($accesspermission as $key => $value) {
            if (!in_array($value->menuid, $menuid)) {
                $this->accesspermission->delete(array('id' => $value->id));
            }
        }

        foreach ($menuid as $key => $value) {
            $check = $this->accesspermission->get(array('userid' => $id, 'menuid' => $value));

            if ($check->num_rows() == 0) {
                $insert = array();
                $insert['userid'] = $id;
                $insert['menuid'] = $value;
                $insert['createddate'] = getCurrentDate();
                $insert['createdby'] = getCurrentIdUser();

                $this->accesspermission->insert($insert);
            }
        }

        // check from accesspermission where userid = $id, jika data yang ada di accesspermission (submenuid) tidak ada pada $submenuid, maka hapus data tersebut, dan jika data $submenuid tidak ada di accesspermission, maka insert data tersebut
        $accesspermission = $this->accesspermission->get(array('userid' => $id, 'submenuid !=' => null))->result();
        foreach ($accesspermission as $key => $value) {
            if (!in_array($value->submenuid, $submenuid)) {
                $this->accesspermission->delete(array('id' => $value->id));
            }
        }

        foreach ($submenuid as $key => $value) {
            $check = $this->accesspermission->get(array('userid' => $id, 'submenuid' => $value));

            if ($check->num_rows() == 0) {
                $insert = array();
                $insert['userid'] = $id;
                $insert['submenuid'] = $value;
                $insert['createddate'] = getCurrentDate();
                $insert['createdby'] = getCurrentIdUser();

                $this->accesspermission->insert($insert);
            }
        }

        return JSONResponseDefault('OK', 'Data berhasil disimpan');
    }

    public function process_delete()
    {
        $id = getPost('id');

        $this->operator_desa->delete(array('id' => $id));

        return JSONResponseDefault('OK', 'Data berhasil dihapus');
    }
}
