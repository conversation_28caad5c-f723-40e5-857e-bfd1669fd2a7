<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Wisata_Model $wisata
 * @property Datatables $datatables
 * @property CI_Upload $upload
 * @property Setting_Umum $settingumum
 * @property Kontak_Penting $kontakpenting
 */
class Wisata extends MY_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->load->model('Wisata_Model', 'wisata');
		$this->load->model('Setting_Umum', 'settingumum');
		$this->load->model('Kontak_Penting', 'kontakpenting');
		$this->load->model('Master_Users', 'masterusers');
	}

	public function index()
	{
		if (!isLogin()) {
			return redirect('auth/login');
		} else if (!isAdmin()) {
			return redirect('dashboard');
		}

		$where = array();
		if (getSessionValue('ROLE') == 'admin') {
			$where['a.id_user'] = getCurrentIdUser();
		}

		$data = array();
		$data['title'] = 'Wisata';
		$data['content'] = 'wisata/index';
		$data['wisata'] = $this->wisata->getDefaultData($where)->result();

		return $this->load->view('master', $data);
	}

	public function datatables()
	{
		if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Session required');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
		}

		$datatables = $this->datatables->make('Wisata_Model', 'QueryDatatables', 'SearchDatatables');

		$where = array();
		if (isAdmin()) {
			$where['a.id_user'] = getCurrentIdUser();
		}

		$data = array();
		foreach ($datatables->getData($where) as $key => $value) {
			if ($value->foto != null) {
				$exploding = explode(',', $value->foto);

				if (isset($exploding[0])) {
					$foto = "<img src=\"" . asset_url($exploding[0]) . "\" width=\"100\">";
				} else {
					$foto = "- Foto Tidak Ditemukan -";
				}
			} else {
				$foto = "- Foto Tidak Ditemukan -";
			}

			$detail = array();
			$detail[] = $foto;
			$detail[] = $value->judul;
			$detail[] = $value->sub_judul;
			$detail[] = "Rp. " . IDR($value->price);
			$detail[] = $value->is_discount == 1 ? $value->discounttype . ' ' . ($value->discounttype == 'Nominal' ? 'Rp' : null) . IDR($value->discount) . ($value->discounttype == 'Persentase' ? '%' : null) : 'Tidak Ada';
			$detail[] = "<a href=\"" . base_url('wisata/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm\">
				<i class=\"fa fa-edit\"></i>
			</a>

			<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteWisata($value->id)\">
				<i class=\"fa fa-trash\"></i>
			</button>";

			$data[] = $detail;
		}

		return $datatables->json($data);
	}

	public function add()
	{
		if (!isLogin()) {
			return redirect('auth/login');
		} else if (!isAdmin()) {
			return redirect('dashboard');
		}

		$data = array();
		$data['title'] = 'Wisata - Add';
		$data['content'] = 'wisata/add';

		return $this->load->view('master', $data);
	}

	public function process_add()
	{
		if (!isPostAjax()) {
			return JSONResponseRequestReject();
		} else if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Session required');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
		}

		$judul = getPost('judul');
		$subjudul = getPost('subjudul');
		$deskripsi = getPost('deskripsi');
		$foto = $_FILES['foto'];
		$lat = getPost('lat');
		$lng = getPost('lng');
		$address = getPost('alamat');
		$price = getPost('price');
		$is_discount = getPost('is_discount');
		$discounttype = getPost('discounttype');
		$discount = getPost('discount');

		if (count($foto['name']) == 0) {
			return JSONResponseDefault('FAILED', 'Foto tidak boleh kosong');
		}

		$images = array();

		foreach ($foto['name'] as $key => $value) {
			$_FILES['images[]']['name'] = $foto['name'][$key];
			$_FILES['images[]']['type']  = $foto['type'][$key];
			$_FILES['images[]']['tmp_name'] = $foto['tmp_name'][$key];
			$_FILES['images[]']['error'] = $foto['error'][$key];
			$_FILES['images[]']['size'] = $foto['size'][$key];

			try {
				$upload = doUpload_CloudStorage('images[]', 'jpg|jpeg|png');
				$file_name = $upload['name'];

				$images[] = $file_name;
			} catch (Exception $ex) {
				return JSONResponseDefault('FAILED', $ex->getMessage());
			}
		}

		$insert = array();
		$insert['judul'] = $judul;
		$insert['deskripsi'] = $deskripsi;
		$insert['sub_judul'] = $subjudul;
		$insert['id_user'] = getCurrentIdUser();
		$insert['createddate'] = getCurrentDate();
		$insert['updateddate'] = getCurrentDate();
		$insert['foto'] = implode(',', $images);
		$insert['lat'] = $lat;
		$insert['lng'] = $lng;
		$insert['address'] = $address;
		$insert['price'] = $price;

		if ($is_discount == 1) {
			if (!in_array($discounttype, ['Persentase', 'Nominal'])) {
				return JSONResponseDefault('FAILED', 'Tipe diskon tidak valid');
			} else if (!is_numeric($discount)) {
				return JSONResponseDefault('FAILED', 'Diskon harus berupa angka');
			} else if ($discount < 0) {
				return JSONResponseDefault('FAILED', 'Diskon tidak boleh kurang dari 0');
			} else if ($discount > 100 && $discounttype == 'Persentase') {
				return JSONResponseDefault('FAILED', 'Diskon tidak boleh lebih dari 100%');
			}

			$insert['is_discount'] = $is_discount;
			$insert['discounttype'] = $discounttype;
			$insert['discount'] = $discount;
		}

		$doInsert = $this->wisata->insert($insert);

		if ($doInsert) {
			return JSONResponseDefault('OK', 'Wisata berhasil ditambahkan');
		} else {
			return JSONResponseDefault('FAILED', 'Gagal menambahkan wisata');
		}
	}

	public function edit($id)
	{
		if (!isLogin()) {
			return redirect('auth/login');
		} else if (!isAdmin()) {
			return redirect('dashboard');
		}

		$get = $this->wisata->getDefaultData(array(
			'a.id' => $id,
			'a.id_user' => getCurrentIdUser(),
		));

		if ($get->num_rows() == 0) {
			return redirect('wisata');
		}

		$row = $get->row();

		$detailwisata = array();
		$exploding = explode(',', $row->foto);

		foreach ($exploding as $key => $value) {
			$detailwisata[] = array(
				'name' => $value,
				'url' => asset_url($value),
			);
		}

		$data = array();
		$data['title'] = 'Wisata - Edit';
		$data['content'] = 'wisata/edit';
		$data['wisata'] = $get->row();
		$data['detailwisata'] = json_encode($detailwisata);

		return $this->load->view('master', $data);
	}

	public function process_edit($id)
	{
		if (!isPostAjax()) {
			return JSONResponseRequestReject();
		} else if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Session required');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
		}

		$get = $this->wisata->getDefaultData(array(
			'a.id' => $id,
			'a.id_user' => getCurrentIdUser(),
		));

		if ($get->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
		}

		$judul = getPost('judul');
		$subjudul = getPost('subjudul');
		$deskripsi = getPost('deskripsi');
		$foto = $_FILES['foto'];
		$lat = getPost('lat');
		$lng = getPost('lng');
		$address = getPost('alamat');
		$price = getPost('price');
		$is_discount = getPost('is_discount');
		$discounttype = getPost('discounttype');
		$discount = getPost('discount');

		if (count($foto['name']) == 0) {
			return JSONResponseDefault('FAILED', 'Foto tidak boleh kosong');
		}

		$update = array();
		$images = array();

		if (!empty($foto['name'][0])) {
			foreach ($foto['name'] as $key => $value) {
				$_FILES['images[]']['name'] = $foto['name'][$key];
				$_FILES['images[]']['type']  = $foto['type'][$key];
				$_FILES['images[]']['tmp_name'] = $foto['tmp_name'][$key];
				$_FILES['images[]']['error'] = $foto['error'][$key];
				$_FILES['images[]']['size'] = $foto['size'][$key];

				try {
					$upload = doUpload_CloudStorage('images[]', 'jpg|jpeg|png');
					$file_name = $upload['name'];

					$images[] = $file_name;
				} catch (Exception $ex) {
					return JSONResponseDefault('FAILED', $ex->getMessage());
				}
			}

			$update['foto'] = implode(',', $images);
		}

		$update['judul'] = $judul;
		$update['sub_judul'] = $subjudul;
		$update['deskripsi'] = $deskripsi;
		$update['updateddate'] = getCurrentDate();
		$update['lat'] = $lat;
		$update['lng'] = $lng;
		$update['address'] = $address;
		$update['price'] = $price;

		if ($is_discount == 1) {
			if (!in_array($discounttype, ['Persentase', 'Nominal'])) {
				return JSONResponseDefault('FAILED', 'Tipe diskon tidak valid');
			} else if (!is_numeric($discount)) {
				return JSONResponseDefault('FAILED', 'Diskon harus berupa angka');
			} else if ($discount < 0) {
				return JSONResponseDefault('FAILED', 'Diskon tidak boleh kurang dari 0');
			} else if ($discount > 100 && $discounttype == 'Persentase') {
				return JSONResponseDefault('FAILED', 'Diskon tidak boleh lebih dari 100%');
			}

			$update['is_discount'] = $is_discount;
			$update['discounttype'] = $discounttype;
			$update['discount'] = $discount;
		} else {
			$update['is_discount'] = 0;
			$update['discounttype'] = null;
			$update['discount'] = null;
		}

		$doUpdate = $this->wisata->update(array(
			'id' => $id
		), $update);

		if ($doUpdate) {
			return JSONResponseDefault('OK', 'Data berhasil diubah');
		} else {
			return JSONResponseDefault('FAILED', 'Gagal mengubah data');
		}
	}

	public function process_delete()
	{
		if (!isPostAjax()) {
			return JSONResponseRequestReject();
		} else if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Session required');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
		}

		$id = getPost('id');

		$get = $this->wisata->getDefaultData(array(
			'a.id' => $id,
			'a.id_user' => getCurrentIdUser(),
		));

		if ($get->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
		}

		$delete = $this->wisata->delete(array(
			'id' => $id
		));

		if ($delete) {
			return JSONResponseDefault('OK', 'Data berhasil dihapus');
		} else {
			return JSONResponseDefault('FAILED', 'Gagal menghapus data');
		}
	}

	public function view_wisata($id)
	{
		$get = $this->wisata->getDefaultData(array(
			'a.id' => $id
		));

		if ($get->num_rows() == 0) {
			return redirect(base_url());
		}

		$where = array(
			'a.id_user' => $this->subdomain_account->id
		);

		$setting = $this->settingumum->getDefaultData($where);
		$data['title'] = $get->row()->judul;
		$data['wisata'] = $get->row();
		$data['setting'] = $setting->row();
		$data['kontakpenting'] = $this->kontakpenting->getDefaultData($where)->result();
		$data['other'] = $this->wisata->getDefaultData(array(
			'a.id !=' => $id,
			'a.id_user' => $this->subdomain_account->id
		))->result();

		if ($setting->num_rows() > 0) {
			if ($this->subdomain_account->themeid == 2) {
				$data['content'] = 'profile/wisata';

				return $this->load->view('profile/master', $data);
			} else {
		$data['content'] = 'landing/wisata';

			return $this->load->view('landing/master', $data);
			}
		} else {
			return $this->load->view('profile_v2/configuration', $data);
		}
	}
}
