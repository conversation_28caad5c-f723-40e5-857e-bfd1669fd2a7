<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Flow_OrganizationChart $flow_organization_chart
 */
class FlowOrganizationChart extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Flow_OrganizationChart', 'flow_organization_chart');
    }

    public function index()
    {
        if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Alur Jabatan Struktur Organisasi';
        $data['content'] = 'flow_organization_chart/index';
        $data['floworganizationchart'] = $this->flow_organization_chart->select('a.*, b.name AS parentname')
            ->join('floworganizationchart b', 'b.id = a.parent', 'LEFT')
            ->result(array(
                'a.createdby' => getCurrentIdUser()
            ));

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Alur Jabatan Struktur Organisasi';
        $data['content'] = 'flow_organization_chart/add';
        $data['floworganizationchart'] = $this->flow_organization_chart->result(array('createdby' => getCurrentIdUser()));

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $name = getPost('name');
        $parent = getPost('parent');

        if ($parent != null) {
            $get = $this->flow_organization_chart->get(array('id' => $parent, 'createdby' => getCurrentIdUser()));

            if ($get->num_rows() == 0) {
                return JSONResponseDefault('FAILED', 'Parent tidak ditemukan!');
            }
        }

        $insert = array();
        $insert['name'] = $name;
        $insert['parent'] = $parent;
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $this->flow_organization_chart->insert($insert);

        return JSONResponseDefault('OK', 'Berhasil menambahkan data!');
    }

    public function process_delete()
    {
        if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $id = getPost('id');

        $get = $this->flow_organization_chart->get(array('id' => $id, 'createdby' => getCurrentIdUser()));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan!');
        }

        $this->flow_organization_chart->delete(array('id' => $id, 'createdby' => getCurrentIdUser()));

        return JSONResponseDefault('OK', 'Berhasil menghapus data!');
    }

    public function edit($id)
    {
        if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->flow_organization_chart->get(array('id' => $id, 'createdby' => getCurrentIdUser()));

        if ($get->num_rows() == 0) {
            return redirect(base_url('floworganizationchart'));
        }

        $data = array();
        $data['title'] = 'Edit Alur Jabatan Struktur Organisasi';
        $data['content'] = 'flow_organization_chart/edit';
        $data['floworganizationchart'] = $this->flow_organization_chart->result(array('createdby' => getCurrentIdUser(), 'id !=' => $id));
        $data['get'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $get = $this->flow_organization_chart->get(array('id' => $id, 'createdby' => getCurrentIdUser()));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan!');
        }

        $name = getPost('name');
        $parent = getPost('parent');

        if ($parent != null) {
            $get = $this->flow_organization_chart->get(array('id' => $parent, 'createdby' => getCurrentIdUser(), 'id !=' => $id));

            if ($get->num_rows() == 0) {
                return JSONResponseDefault('FAILED', 'Parent tidak ditemukan!');
            }
        }

        $update = array();
        $update['name'] = $name;
        $update['parent'] = $parent;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->flow_organization_chart->update(array('id' => $id, 'createdby' => getCurrentIdUser()), $update);

        return JSONResponseDefault('OK', 'Berhasil mengubah data!');
    }
}
