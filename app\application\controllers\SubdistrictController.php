<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Warga_Model $warga
 * @property Master_Users $masterusers
 */
class SubdistrictController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Warga_Model', 'warga');
        $this->load->model('Master_Users', 'masterusers');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = 'Manage Kecamatan';
        $data['content'] = 'manage/subdistrict/index';
        $data['subdistrict'] = $this->masterusers->select('a.*, b.nama_kabkota AS kabupaten,c.nama_kecamatan AS kecamatan')
            ->join('kabkota b', 'b.id_kabkota = a.kabkotaid')
            ->join('kecamatan c', 'c.id_kecamatan = a.kecamatanid')
            ->getDefaultData(array('role' => 'kecamatan'))->result();

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = 'Manage Kecamatan - Add';
        $data['content'] = 'manage/subdistrict/add';
        $data['kabkota'] = $this->warga->getKabkota(array('id_propinsi' => 22))->result();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menambahkan kecamatan');
        }

        $username = getPost('username');
        $password = getPost('password');
        $kabupaten = getPost('kabupaten');
        $kecamatan = getPost('kecamatan');

        $users = $this->masterusers->getDefaultData(array(
            'username' => $username
        ));

        if ($users->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Username yang anda masukkan telah terdaftar');
        }

        $cekkecamatan = $this->masterusers->getDefaultData(array(
            'kecamatanid' => $kecamatan,
            'role' => 'kecamatan'
        ));

        if ($cekkecamatan->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Kecamatan yang anda masukkan telah terdaftar');
        }

        $insert = array();
        $insert['username'] = $username;
        $insert['password'] = md5($password);
        $insert['role'] = 'kecamatan';
        $insert['kabkotaid'] = $kabupaten;
        $insert['kecamatanid'] = $kecamatan;

        $insert = $this->masterusers->insert($insert);

        if ($insert) {
            return JSONResponseDefault('OK', 'Berhasil menambahkan kecamatan');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menambahkan kecamatan');
        }
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menghapus kecamatan');
        }

        $id = getPost('id');

        $delete = $this->masterusers->delete(array('id' => $id));

        if ($delete) {
            return JSONResponseDefault('OK', 'Berhasil menghapus kecamatan');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus kecamatan');
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin()) {
            return redirect('dashboard');
        }

        $get = $this->masterusers->getDefaultData(array(
            'a.id' => $id
        ));

        if ($get->num_rows() == 0) {
            return redirect('manage/admin');
        }

        $data = array();
        $data['title'] = 'Manage Kecamatan - Edit';
        $data['content'] = 'manage/subdistrict/edit';
        $data['subdistrict'] = $get->row();
        $data['kabkota'] = $this->warga->getKabkota(array('id_propinsi' => 22))->result();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk mengubah Kecamatan');
        }

        $username = getPost('username');
        $password = getPost('password');
        $kabupaten = getPost('kabupaten');
        $kecamatan = getPost('kecamatan');

        $getsubdistrict = $this->masterusers->getDefaultData(array('id' => $id));

        if ($getsubdistrict->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Kecamatan tidak ditemukan');
        }

        $row = $getsubdistrict->row();

        if ($row->username != $username) {
            $cekusername = $this->masterusers->getDefaultData(array('username' => $username));

            if ($cekusername->num_rows() > 0) {
                return JSONResponseDefault('FAILED', 'Username yang anda masukkan telah terdaftar');
            }
        } else if ($row->kecamatanid != $kecamatan) {
            $cekkecamatan = $this->masterusers->getDefaultData(array(
                'kecamatanid' => $kecamatan,
                'role' => 'kecamatan'
            ));

            if ($cekkecamatan->num_rows() > 0) {
                return JSONResponseDefault('FAILED', 'Kecamatan yang anda masukkan telah terdaftar');
            }
        }

        $update = array();
        $update['username'] = $username;
        $update['kabkotaid'] = $kabupaten;
        $update['kecamatanid'] = $kecamatan;

        if ($password != null) {
            $update['password'] = md5($password);
        }

        $update = $this->masterusers->update(array('id' => $id), $update);

        if ($update) {
            return JSONResponseDefault('OK', 'Berhasil mengubah kecamatan');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal mengubah kecamatan');
        }
    }
}
