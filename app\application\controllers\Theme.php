<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsTheme $mstheme
 * @property Master_Users $msusers
 */
class Theme extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsTheme', 'mstheme');
        $this->load->model('Master_Users', 'msusers');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Tema';
        $data['content'] = 'theme/index';
        $data['themes'] = $this->mstheme->result();

        return $this->load->view('master', $data);
    }

    public function set()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Silahkan login terlebih dahulu');
        }

        $id = getPost('id');

        if ($id == null) {
            return JSONResponseDefault('FAILED', 'ID Tema tidak boleh kosong');
        }

        $theme = $this->mstheme->get(array('id' => $id));

        if ($theme->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Tema tidak ditemukan');
        }

        $theme = $theme->row();

        $this->msusers->update(array('id' => getCurrentIdUser()), array('themeid' => $theme->id));

        return JSONResponseDefault('OK', 'Tema berhasil diubah');
    }
}
