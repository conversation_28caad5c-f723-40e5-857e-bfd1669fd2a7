/* PROGIDES - Village Information System Styles */

/* CSS Variables for consistent theming */
:root {
    --primary-color: #6e9e28;
    --primary-dark: #5a7f20;
    --primary-light: #8bb83a;
}

/* Primary color utilities */
.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-primary-dark {
    background-color: var(--primary-dark) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* Hover states */
.hover\:bg-primary:hover {
    background-color: var(--primary-color) !important;
}

.hover\:bg-primary-dark:hover {
    background-color: var(--primary-dark) !important;
}

.hover\:text-primary:hover {
    color: var(--primary-color) !important;
}

/* Custom animations and transitions */
.transition-all {
    transition: all 0.3s ease;
}

/* Card hover effects */
.card-hover {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Image hover effects */
.image-hover {
    transition: transform 0.3s ease;
}

.image-hover:hover {
    transform: scale(1.05);
}

/* Button styles */
.btn-primary {
    background-color: var(--primary-color);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 9999px;
    font-weight: 500;
    transition: background-color 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: white;
    color: var(--primary-color);
    padding: 0.5rem 1.5rem;
    border-radius: 9999px;
    font-weight: 500;
    transition: background-color 0.3s ease;
    border: 1px solid var(--primary-color);
    cursor: pointer;
}

.btn-secondary:hover {
    background-color: #f3f4f6;
}

/* Navigation styles */
.nav-link {
    color: white;
    text-decoration: none;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-light);
}

.nav-link.active {
    background-color: rgba(110, 158, 40, 0.2) !important;
    font-weight: 500;
}

/* Hero section styles */
.hero-overlay {
    background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3));
}

/* Service card styles */
.service-card {
    background-color: var(--primary-color);
    padding: 1.5rem;
    border-radius: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    transform: scale(1);
}

.service-card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: scale(1.05);
}

.service-icon {
    width: 4rem;
    height: 4rem;
    background-color: white;
    border-radius: 0.5rem;
    margin: 0 auto 0.75rem auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Product card styles */
.product-card {
    background-color: white;
    border-radius: 1rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: box-shadow 0.3s ease;
}

.product-card:hover {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Statistics section */
.stats-section {
    background-color: var(--primary-color);
    color: white;
}

/* Footer styles */
.footer {
    background-color: #f3f4f6;
    padding: 3rem 0;
}

.footer-link {
    color: #6b7280;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: var(--primary-color);
}

/* Form styles */
.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 0.5rem;
    outline: none;
    transition: all 0.3s ease;
}

.form-input:focus {
    ring: 2px;
    ring-color: white;
    background-color: white;
}

.form-textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 0.5rem;
    outline: none;
    resize: none;
    transition: all 0.3s ease;
}

.form-textarea:focus {
    ring: 2px;
    ring-color: white;
    background-color: white;
}

/* Responsive utilities */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none;
    }

    .mobile-menu {
        display: block;
    }
}

@media (min-width: 769px) {
    .desktop-hidden {
        display: none;
    }

    .mobile-menu {
        display: none;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Loading animation */
.loading {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: .5;
    }
}

/* Fade in animation */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Slide in animation */
.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }

    to {
        transform: translateX(0);
    }
}

/* Additional utility classes for consistency */
.section-padding {
    padding: 4rem 0;
}

.container-custom {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.text-section-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.text-section-subtitle {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.grid-auto-fit {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.shadow-custom {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-custom-hover:hover {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Navigation specific styles */
.navbar {
    position: relative;
    z-index: 50;
}

.navbar-transparent {
    background: transparent;
    position: absolute;
    width: 100%;
    top: 0;
}

.navbar-solid {
    background-color: var(--primary-color);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Hero section specific */
.hero-section {
    height: 100vh;
    background-size: cover;
    background-position: center;
    position: relative;
}

.hero-content {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Card components */
.card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-image {
    width: 100%;
    height: 16rem;
    object-fit: cover;
    border-radius: 0.5rem;
}

/* Government page specific styles */
.government-illustration {
    background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
    border-radius: 1.5rem;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.officer-card {
    text-align: center;
    transition: transform 0.3s ease;
}

.officer-card:hover {
    transform: translateY(-5px);
}

.officer-image {
    width: 100%;
    height: 16rem;
    object-fit: cover;
    border-radius: 1rem;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.officer-card:hover .officer-image {
    transform: scale(1.05);
}

/* Responsive text sizes */
@media (max-width: 640px) {
    .text-section-title {
        font-size: 1.875rem;
    }

    .hero-section {
        height: 80vh;
    }
}

/* Legal products page specific styles */
.legal-document-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.legal-document-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.legal-icon {
    transition: transform 0.3s ease;
}

.legal-document-card:hover .legal-icon {
    transform: scale(1.1);
}

/* Modal styles */
.modal-backdrop {
    backdrop-filter: blur(4px);
}

/* Document detail styles */
.document-item {
    transition: background-color 0.3s ease;
}

.document-item:hover {
    background-color: #f9fafb;
}

.download-btn {
    transition: all 0.3s ease;
}

.download-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(110, 158, 40, 0.3);
}

@media (max-width: 480px) {
    .text-section-title {
        font-size: 1.5rem;
    }

    .section-padding {
        padding: 2rem 0;
    }
}