<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>

<!-- Hero Section -->
<section class="relative h-96 bg-cover bg-center"
    style="background-image: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');">
    <div class="absolute inset-0 flex items-center justify-center">
        <div class="text-center text-white max-w-4xl mx-auto px-4">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                <PERSON><PERSON><PERSON><PERSON> <?= $user->admintype != '<PERSON><PERSON><PERSON>an' ? 'Desa' : '<PERSON><PERSON><PERSON>an' ?>
            </h1>
            <p class="text-xl md:text-2xl font-light">
                Transparansi <PERSON> <?= isset($setting->desa) ? $setting->desa : 'Desa' ?>
            </p>
        </div>
    </div>
</section>

<!-- Filter Section -->
<section class="py-12 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">Filter Data Anggaran</h2>
            
            <form action="<?= base_url(uri_string()) ?>" method="GET" autocomplete="off">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <!-- Tahun -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tahun</label>
                        <select name="tahun" id="tahun" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="" selected disabled>Pilih Tahun</option>
                            <?php foreach ($tahun as $t) : ?>
                                <option value="<?= $t->tahun ?>" <?= $current_tahun == $t->tahun ? 'selected' : null ?>><?= $t->tahun ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Bidang -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Bidang</label>
                        <select name="bidang" id="bidang" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="">- Pilih Semua -</option>
                        </select>
                    </div>

                    <!-- Sub Bidang -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Sub Bidang</label>
                        <select name="subbidang" id="subbidang" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="">- Pilih Semua -</option>
                        </select>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-end space-x-3">
                        <button type="submit" class="bg-primary hover:bg-primary-dark text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300 flex-1">
                            <i class="fas fa-filter mr-2"></i>Filter
                        </button>
                        <button type="button" onclick="printPdf()" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300">
                            <i class="fas fa-print"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Budget Data Section -->
<section class="py-12 bg-white">
    <div class="container mx-auto px-4">
        <?php if (!empty($header)) : ?>
            <?php foreach ($header as $key => $value) : ?>
                <?php
                $where_detail = "";
                if ($current_subbidang != null) {
                    $where_detail = "AND b.detailkategorianggaranid = '$current_subbidang'";
                }
                $detail = $this->db->query("SELECT SUM(a.jumlah) AS jumlah, SUM(a.realisasi) AS realisasi, SUM(a.jumlah_menjadi) AS jumlah_menjadi, c.kode, c.nama AS activity FROM apbdetaildesa a JOIN subdetail_kategori_anggaran b ON b.id = a.subdetailid JOIN detail_kategori_anggaran c ON c.id = b.detailkategorianggaranid WHERE a.headerid = $value->id $where_detail GROUP BY c.kode, c.nama ORDER BY c.kode ASC");

                $percentage = 0;
                $total = 0;
                $realisasi = 0;
                $jumlah_menjadi = 0;

                foreach ($detail->result() as $k => $v) {
                    $total += $v->jumlah;
                    $realisasi += $v->realisasi;
                    $jumlah_menjadi += $v->jumlah_menjadi;
                }

                try {
                    $percentage = ($realisasi / $total) * 100;
                } catch (DivisionByZeroError $e) {
                    $percentage = 0;
                }
                ?>

                <div class="bg-white rounded-xl shadow-lg p-8 mb-8 border border-gray-100">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">
                        <?= $value->nama_kategori_anggaran ?? $value->nama ?>
                    </h3>

                    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                        <!-- Progress Chart -->
                        <div class="text-center">
                            <div class="relative inline-flex items-center justify-center w-32 h-32 mx-auto mb-4">
                                <svg class="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                                    <path class="text-gray-200" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    <path class="text-primary" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="<?= round($percentage, 2) ?>, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="text-2xl font-bold text-primary"><?= round($percentage, 2) ?>%</span>
                                </div>
                            </div>
                            <p class="text-gray-600 font-medium">Realisasi Anggaran</p>
                        </div>

                        <!-- Budget Summary Cards -->
                        <div class="lg:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- Total Budget -->
                            <div class="bg-blue-50 rounded-lg p-6 text-center">
                                <div class="w-12 h-12 bg-blue-500 rounded-lg mx-auto mb-3 flex items-center justify-center">
                                    <i class="fas fa-wallet text-white text-xl"></i>
                                </div>
                                <h4 class="text-sm font-medium text-gray-600 mb-2">Anggaran Semula</h4>
                                <p class="text-xl font-bold text-blue-600">Rp <?= number_format($total, 0, ',', '.') ?></p>
                            </div>

                            <!-- Revised Budget -->
                            <div class="bg-green-50 rounded-lg p-6 text-center">
                                <div class="w-12 h-12 bg-green-500 rounded-lg mx-auto mb-3 flex items-center justify-center">
                                    <i class="fas fa-edit text-white text-xl"></i>
                                </div>
                                <h4 class="text-sm font-medium text-gray-600 mb-2">Anggaran Menjadi</h4>
                                <p class="text-xl font-bold text-green-600">Rp <?= number_format($jumlah_menjadi, 0, ',', '.') ?></p>
                            </div>

                            <!-- Realization -->
                            <div class="bg-orange-50 rounded-lg p-6 text-center">
                                <div class="w-12 h-12 bg-orange-500 rounded-lg mx-auto mb-3 flex items-center justify-center">
                                    <i class="fas fa-chart-line text-white text-xl"></i>
                                </div>
                                <h4 class="text-sm font-medium text-gray-600 mb-2">Terealisasi</h4>
                                <p class="text-xl font-bold text-orange-600">Rp <?= number_format($realisasi, 0, ',', '.') ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Detail Table -->
                    <div class="mt-8 overflow-x-auto">
                        <table class="w-full bg-white rounded-lg overflow-hidden shadow-sm">
                            <thead class="bg-primary text-white">
                                <tr>
                                    <th class="px-6 py-4 text-left text-sm font-medium">Kode Bidang</th>
                                    <th class="px-6 py-4 text-left text-sm font-medium">Nama Bidang/Sub Bidang</th>
                                    <th class="px-6 py-4 text-center text-sm font-medium">Anggaran (Semula)</th>
                                    <th class="px-6 py-4 text-center text-sm font-medium">Anggaran (Menjadi)</th>
                                    <th class="px-6 py-4 text-center text-sm font-medium">Terealisasi</th>
                                    <th class="px-6 py-4 text-center text-sm font-medium">Lebih/(Kurang)</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <?php foreach ($detail->result() as $k => $v) : ?>
                                    <?php if ($v->jumlah == 0) continue; ?>
                                    <tr class="hover:bg-gray-50 transition-colors duration-200">
                                        <td class="px-6 py-4 text-sm text-gray-900"><?= $v->kode ?? '-' ?></td>
                                        <td class="px-6 py-4 text-sm text-gray-900"><?= $v->activity ?></td>
                                        <td class="px-6 py-4 text-sm text-center text-gray-900">Rp <?= number_format($v->jumlah, 0, ',', '.') ?></td>
                                        <td class="px-6 py-4 text-sm text-center text-gray-900">Rp <?= number_format($v->jumlah_menjadi, 0, ',', '.') ?></td>
                                        <td class="px-6 py-4 text-sm text-center text-gray-900">Rp <?= number_format($v->realisasi, 0, ',', '.') ?></td>
                                        <td class="px-6 py-4 text-sm text-center">
                                            <?php if (($v->jumlah - $v->realisasi) < 0) : ?>
                                                <span class="inline-flex px-3 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                                                    Rp <?= number_format(abs($v->jumlah - $v->realisasi), 0, ',', '.') ?>
                                                </span>
                                            <?php else : ?>
                                                <span class="inline-flex px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                                    Rp <?= number_format(abs($v->realisasi - $v->jumlah), 0, ',', '.') ?>
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else : ?>
            <div class="bg-white rounded-xl shadow-lg p-12 text-center">
                <div class="w-20 h-20 bg-gray-100 rounded-full mx-auto mb-6 flex items-center justify-center">
                    <i class="fas fa-chart-pie text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">Belum Ada Data Anggaran</h3>
                <p class="text-gray-600">Data anggaran untuk tahun yang dipilih belum tersedia.</p>
            </div>
        <?php endif; ?>
    </div>
</section>

<script>
// JavaScript for dynamic dropdowns and functionality
let kategori_anggaran = <?= $kategori_anggaran ?>;

function printPdf() {
    let tahun = document.getElementById('tahun').value;
    let bidang = document.getElementById('bidang').value;
    let subbidang = document.getElementById('subbidang').value;
    
    let url = '<?= base_url('pengelolaandana/cetak') ?>?tahun=' + tahun;
    if (bidang) url += '&bidang=' + bidang;
    if (subbidang) url += '&subbidang=' + subbidang;
    
    window.open(url, '_blank');
}

// Initialize dropdowns
document.addEventListener('DOMContentLoaded', function() {
    populateBidang();
    
    document.getElementById('bidang').addEventListener('change', function() {
        populateSubBidang();
    });
});

function populateBidang() {
    let bidangSelect = document.getElementById('bidang');
    bidangSelect.innerHTML = '<option value="">- Pilih Semua -</option>';
    
    kategori_anggaran.forEach(function(item) {
        let option = document.createElement('option');
        option.value = item.id;
        option.textContent = item.nama;
        if ('<?= $current_bidang ?>' == item.id) {
            option.selected = true;
        }
        bidangSelect.appendChild(option);
    });
    
    if ('<?= $current_bidang ?>') {
        populateSubBidang();
    }
}

function populateSubBidang() {
    let bidangId = document.getElementById('bidang').value;
    let subbidangSelect = document.getElementById('subbidang');
    subbidangSelect.innerHTML = '<option value="">- Pilih Semua -</option>';
    
    if (bidangId) {
        let selectedBidang = kategori_anggaran.find(item => item.id == bidangId);
        if (selectedBidang && selectedBidang.detail) {
            selectedBidang.detail.forEach(function(detail) {
                let option = document.createElement('option');
                option.value = detail.id;
                option.textContent = detail.nama;
                if ('<?= $current_subbidang ?>' == detail.id) {
                    option.selected = true;
                }
                subbidangSelect.appendChild(option);
            });
        }
    }
}
</script>
