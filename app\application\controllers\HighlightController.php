<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsHighlight $highlight
 * @property Datatables $datatables
 * @property CI_Upload $upload
 * @property Setting_Umum $settingumum
 * @property Kontak_Penting $kontakpenting
 */
class HighlightController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsHighlight', 'highlight');
        $this->load->model('Setting_Umum', 'settingumum');
        $this->load->model('Kontak_Penting', 'kontakpenting');
        $this->load->model('Master_Users', 'masterusers');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Highlight';
        $data['content'] = 'highlight/index';
        $data['highlight'] = $this->highlight->result(array(
            'id_user' => getCurrentIdUser()
        ));

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $datatables = $this->datatables->make('MsHighlight', 'QueryDatatables', 'SearchDatatables');

        $data = array();
        foreach ($datatables->getData(array('a.id_user' => getCurrentIdUser())) as $key => $value) {
            if ($value->foto != null) {
                $foto = "<img src=\"" . asset_url($value->foto) . "\" width=\"100\">";
            } else {
                $foto = "- Foto Tidak Ditemukan -";
            }

            $detail = array();
            $detail[] = DateFormat($value->tanggal, 'd F Y');
            $detail[] = $value->judul;
            $detail[] = $foto;
            $detail[] = "<a href=\"" . base_url('highlight/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm\">
                <i class=\"fa fa-edit\"></i>
            </a>

            <button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteHighlight($value->id)\">
                <i class=\"fa fa-trash\"></i>
            </button>";

            $data[] = $detail;
        }

        return $datatables->json($data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Highlight';
        $data['content'] = 'highlight/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $judul = getPost('judul');
        $deskripsi = getPost('deskripsi');

        $insert = array();

        try {
            $upload = doUpload_CloudStorage('foto', 'jpg|jpeg|png');
            $insert['foto'] = $upload['name'];
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }

        $insert['judul'] = $judul;
        $insert['tanggal'] = getCurrentDate();
        $insert['deskripsi'] = $deskripsi;
        $insert['id_user'] = getCurrentIdUser();

        $doInsert = $this->highlight->insert($insert);

        if ($doInsert) {
            return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menambahkan data');
        }
    }

    public function process_delete()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->highlight->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        $delete = $this->highlight->delete(array(
            'id' => $id
        ));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus data');
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin()) {
            return redirect('dashboard');
        }

        $get = $this->highlight->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect('highlight');
        }

        $data = array();
        $data['title'] = 'Highlight - Edit';
        $data['content'] = 'highlight/edit';
        $data['highlight'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->highlight->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $judul = getPost('judul');
        $deskripsi = getPost('deskripsi');

        $update = array();
        if (isset($_FILES['foto']) && $_FILES['foto']['size'] > 0) {
            try {
                $upload = doUpload_CloudStorage('foto', 'jpg|jpeg|png');
                $update['foto'] = $upload['name'];
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        }

        $update['judul'] = $judul;
        $update['deskripsi'] = $deskripsi;

        $doUpdate = $this->highlight->update(array(
            'id' => $id
        ), $update);

        if ($doUpdate) {
            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal mengubah data');
        }
    }

    public function content($id)
    {
        $data = array();

        $get = $this->highlight->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => $this->subdomain_account->id
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url());
        }

        $where = array(
            'a.id_user' => $this->subdomain_account->id
        );

        $setting = $this->settingumum->getDefaultData($where);
        $data['highlight'] = $get->row();
        $data['title'] = $data['highlight']->judul;
        $data['setting'] = $setting->row();
        $data['kontakpenting'] = $this->kontakpenting->getDefaultData($where)->result();
        $data['content'] = 'profile/highlight';

        if ($setting->num_rows() > 0) {
            return $this->load->view('profile/master', $data);
        } else {
            return $this->load->view('profile_v2/configuration', $data);
        }
    }
}
