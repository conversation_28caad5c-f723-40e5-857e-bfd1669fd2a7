<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>

<!-- Hero Section -->
<section class="relative h-screen bg-cover bg-center"
    style="background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url('https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80');">
    <div class="absolute inset-0 flex items-center justify-center">
        <div class="text-center text-white max-w-4xl mx-auto px-4">
            <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
                Sistem Informasi<br>
                Digital Desa
            </h1>
            <p class="text-xl md:text-2xl font-light">
                <?= isset($setting->kabupaten) ? $setting->kabupaten : 'Kabupaten Hulu Sungai Selatan' ?>
            </p>
        </div>
    </div>
</section>

<!-- Services Grid -->
<section class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-left mb-12">
            <p class="text-primary font-medium mb-2">Layanan Unggulan</p>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-800">Fitur Layanan Pemerintah Desa</h2>
        </div>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-6xl mx-auto">
            <!-- Produk Hukum -->
            <a href="<?= base_url('produk/hukum') ?>"
                class="bg-primary p-6 rounded-3xl text-center hover:shadow-lg transition duration-300 transform hover:scale-105 block">
                <div class="mb-4">
                    <div class="w-16 h-16 bg-white rounded-lg mx-auto flex items-center justify-center mb-3">
                        <i class="fas fa-gavel text-primary text-2xl"></i>
                    </div>
                    <h3 class="font-bold text-white text-lg mb-1">Produk</h3>
                    <h3 class="font-bold text-white text-lg">Hukum</h3>
                </div>
                <button
                    class="bg-white text-primary w-full py-2 rounded-full font-medium hover:bg-gray-100 transition duration-300">
                    Open
                </button>
            </a>

            <!-- Belanja Produk -->
            <a href="<?= base_url('belanja') ?>"
                class="bg-primary p-6 rounded-3xl text-center hover:shadow-lg transition duration-300 transform hover:scale-105 block">
                <div class="mb-4">
                    <div class="w-16 h-16 bg-white rounded-lg mx-auto flex items-center justify-center mb-3">
                        <i class="fas fa-shopping-cart text-red-500 text-2xl"></i>
                    </div>
                    <h3 class="font-bold text-white text-lg mb-1">Belanja</h3>
                    <h3 class="font-bold text-white text-lg">Produk</h3>
                </div>
                <button
                    class="bg-white text-primary w-full py-2 rounded-full font-medium hover:bg-gray-100 transition duration-300">
                    Open
                </button>
            </a>

            <!-- Berita Desa -->
            <div
                class="bg-primary p-6 rounded-3xl text-center hover:shadow-lg transition duration-300 transform hover:scale-105">
                <div class="mb-4">
                    <div class="w-16 h-16 bg-white rounded-lg mx-auto flex items-center justify-center mb-3">
                        <i class="fas fa-newspaper text-blue-500 text-2xl"></i>
                    </div>
                    <h3 class="font-bold text-white text-lg mb-1">Berita</h3>
                    <h3 class="font-bold text-white text-lg">Desa</h3>
                </div>
                <button
                    class="bg-white text-primary w-full py-2 rounded-full font-medium hover:bg-gray-100 transition duration-300">
                    Open
                </button>
            </div>

            <!-- Infografis Desa -->
            <div
                class="bg-primary p-6 rounded-3xl text-center hover:shadow-lg transition duration-300 transform hover:scale-105">
                <div class="mb-4">
                    <div class="w-16 h-16 bg-white rounded-lg mx-auto flex items-center justify-center mb-3">
                        <i class="fas fa-chart-pie text-orange-500 text-2xl"></i>
                    </div>
                    <h3 class="font-bold text-white text-lg mb-1">Infografis</h3>
                    <h3 class="font-bold text-white text-lg">Desa</h3>
                </div>
                <a href="<?= base_url('infografi') ?>"
                    class="bg-white text-primary w-full py-2 rounded-full font-medium hover:bg-gray-100 transition duration-300 inline-block">
                    Open
                </a>
            </div>

            <!-- Pembuatan Surat -->
            <div
                class="bg-primary p-6 rounded-3xl text-center hover:shadow-lg transition duration-300 transform hover:scale-105">
                <div class="mb-4">
                    <div class="w-16 h-16 bg-white rounded-lg mx-auto flex items-center justify-center mb-3">
                        <i class="fas fa-file-alt text-orange-400 text-2xl"></i>
                    </div>
                    <h3 class="font-bold text-white text-lg mb-1">Pembuatan</h3>
                    <h3 class="font-bold text-white text-lg">Surat</h3>
                </div>
                <button
                    class="bg-white text-primary w-full py-2 rounded-full font-medium hover:bg-gray-100 transition duration-300">
                    Open
                </button>
            </div>

            <!-- Buku Tamu -->
            <div
                class="bg-primary p-6 rounded-3xl text-center hover:shadow-lg transition duration-300 transform hover:scale-105">
                <div class="mb-4">
                    <div class="w-16 h-16 bg-white rounded-lg mx-auto flex items-center justify-center mb-3">
                        <i class="fas fa-book text-blue-400 text-2xl"></i>
                    </div>
                    <h3 class="font-bold text-white text-lg mb-1">Buku</h3>
                    <h3 class="font-bold text-white text-lg">Tamu</h3>
                </div>
                <button
                    class="bg-white text-primary w-full py-2 rounded-full font-medium hover:bg-gray-100 transition duration-300">
                    Open
                </button>
            </div>

            <!-- Anggaran Desa -->
            <div
                class="bg-primary p-6 rounded-3xl text-center hover:shadow-lg transition duration-300 transform hover:scale-105">
                <div class="mb-4">
                    <div class="w-16 h-16 bg-white rounded-lg mx-auto flex items-center justify-center mb-3">
                        <i class="fas fa-calculator text-primary text-2xl"></i>
                    </div>
                    <h3 class="font-bold text-white text-lg mb-1">Anggaran</h3>
                    <h3 class="font-bold text-white text-lg">Desa</h3>
                </div>
                <button
                    class="bg-white text-primary w-full py-2 rounded-full font-medium hover:bg-gray-100 transition duration-300">
                    Open
                </button>
            </div>

            <!-- Pemerintahan Desa -->
            <div
                class="bg-primary p-6 rounded-3xl text-center hover:shadow-lg transition duration-300 transform hover:scale-105">
                <div class="mb-4">
                    <div class="w-16 h-16 bg-white rounded-lg mx-auto flex items-center justify-center mb-3">
                        <i class="fas fa-building text-gray-600 text-2xl"></i>
                    </div>
                    <h3 class="font-bold text-white text-lg mb-1">Pemerintahan</h3>
                    <h3 class="font-bold text-white text-lg">Desa</h3>
                </div>
                <button
                    class="bg-white text-primary w-full py-2 rounded-full font-medium hover:bg-gray-100 transition duration-300">
                    Open
                </button>
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row items-center gap-12">
            <div class="lg:w-1/2">
                <div class="mb-6">
                    <p class="text-primary font-medium mb-2">Sambutan-sambutan</p>
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-6">
                        Sambutan <?= isset($user->admintype) && $user->admintype == 'Kelurahan' ? 'Lurah' : 'Kepala Desa' ?>
                    </h2>
                </div>

                <div class="space-y-4 text-gray-600 leading-relaxed">
                    <p class="font-semibold text-gray-800">
                        Selamat datang di website resmi <?= isset($setting->desa) ? $setting->desa : 'Desa Kami' ?>.
                    </p>

                    <?php if (isset($setting->sambutan) && !empty($setting->sambutan)) : ?>
                        <div class="prose max-w-none">
                            <?= $setting->sambutan ?>
                        </div>
                    <?php else : ?>
                        <p>
                            Sebagai <?= isset($user->admintype) && $user->admintype == 'Kelurahan' ? 'Lurah' : 'Kepala Desa' ?>,
                            saya sangat bangga dan senang dapat menyambut Anda di sini.
                            <?= isset($setting->desa) ? $setting->desa : 'Desa Kami' ?> merupakan desa yang dikenal dengan keramahan dan kebersamaan warga.
                            Kami percaya bahwa dengan bekerja sama, kita dapat menciptakan desa yang lebih baik dan sejahtera bagi generasi masa depan.
                        </p>

                        <p>
                            Dengan adanya website ini, kami berharap dapat memberikan informasi yang berguna dan akurat
                            bagi masyarakat, serta mempermudah akses bagi masyarakat untuk berinteraksi dan
                            berkoordinasi dengan pemerintah desa.
                        </p>

                        <p>
                            Terima kasih atas kunjungan Anda dan semoga website ini dapat memberikan manfaat bagi Anda
                            semua.
                        </p>
                    <?php endif; ?>
                </div>

                <div class="mt-8">
                    <p class="text-gray-600 italic mb-1">Salam hangat,</p>
                    <p class="font-bold text-gray-800 text-lg">
                        <?= isset($setting->nama) ? $setting->nama : 'Kepala Desa' ?>
                    </p>
                    <p class="text-gray-600">
                        <?= isset($user->admintype) && $user->admintype == 'Kelurahan' ? 'Lurah' : 'Kepala Desa' ?>
                    </p>
                </div>

                <a href="<?= base_url('pemerintah') ?>"
                    class="mt-6 inline-block bg-gray-800 hover:bg-gray-900 text-white px-6 py-3 rounded-lg font-medium transition duration-300">
                    Selengkapnya
                </a>
            </div>

            <div class="lg:w-1/2">
                <div class="relative">
                    <?php if (isset($setting->foto) && !empty($setting->foto)) : ?>
                        <img src="<?= asset_url($setting->foto) ?>"
                            alt="<?= isset($setting->nama) ? $setting->nama : 'Kepala Desa' ?>"
                            class="w-full max-w-md mx-auto object-cover rounded-lg shadow-lg">
                    <?php else : ?>
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80"
                            alt="Kepala Desa"
                            class="w-full max-w-md mx-auto object-cover rounded-lg shadow-lg">
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="py-12 bg-primary text-white">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div class="flex flex-col items-center">
                <div class="w-16 h-16 bg-white rounded-full mb-4"></div>
                <div class="text-3xl font-bold mb-2">100+</div>
                <div class="text-white/80 text-sm">Total Layanan</div>
            </div>
            <div class="flex flex-col items-center">
                <div class="w-16 h-16 bg-white rounded-full mb-4"></div>
                <div class="text-3xl font-bold mb-2">960+</div>
                <div class="text-white/80 text-sm">Tugas Selesai</div>
            </div>
            <div class="flex flex-col items-center">
                <div class="w-16 h-16 bg-white rounded-full mb-4"></div>
                <div class="text-3xl font-bold mb-2">50+</div>
                <div class="text-white/80 text-sm">Layanan Diterima</div>
            </div>
            <div class="flex flex-col items-center">
                <div class="w-16 h-16 bg-white rounded-full mb-4"></div>
                <div class="text-3xl font-bold mb-2">35+</div>
                <div class="text-white/80 text-sm">Kunjungan Tamu</div>
            </div>
        </div>
    </div>
</section>

<!-- News Section -->
<?php if (isset($berita) && count($berita) > 0) : ?>
    <section class="py-16 bg-gray-100">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center mb-12">
                <div>
                    <p class="text-primary font-medium mb-2">Berita Desa</p>
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-800">Informasi dan Kabar Berita</h2>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <?php foreach (array_slice($berita, 0, 3) as $news) : ?>
                    <!-- News Card -->
                    <div
                        class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                        <div class="relative">
                            <img src="<?= asset_url($news->foto) ?>"
                                alt="<?= $news->judul ?>" class="w-full h-64 object-cover">
                        </div>
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-primary mb-2 line-clamp-2">
                                <?= strlen($news->judul) > 60 ? substr($news->judul, 0, 60) . '...' : $news->judul ?>
                            </h3>
                            <p class="text-gray-500 text-sm mb-4 italic">
                                <?= isset($news->kategori_berita) ? $news->kategori_berita : 'Berita Terbaru' ?>
                            </p>
                            <a href="<?= base_url('berita/' . date('Y', strtotime($news->createddate)) . '/' . $news->link_access . '?id=' . $news->id) ?>"
                                class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-full text-sm font-medium transition-colors duration-300 inline-block">
                                Selengkapnya
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
<?php endif; ?>

<!-- Map Section -->
<section class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-left mb-12">
            <p class="text-primary font-medium mb-2">Wilayah Perbatasan</p>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-800">Batas <?= isset($setting->desa) ? $setting->desa : 'Desa Kandangan Baru' ?></h2>
        </div>
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="h-96 md:h-[500px] relative">
                <?php if (isset($coords) && !empty($coords)) : ?>
                    <div id="map" class="w-full h-full"></div>
                <?php else : ?>
                    <iframe
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d127748.54853271728!2d114.8471!3d-2.7410!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x2e0d6f7c5b5b5b5b%3A0x5b5b5b5b5b5b5b5b!2sKandangan%2C%20Hulu%20Sungai%20Selatan%20Regency%2C%20South%20Kalimantan!5e0!3m2!1sen!2sid!4v1234567890123!5m2!1sen!2sid"
                        width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy"
                        referrerpolicy="no-referrer-when-downgrade" class="w-full h-full">
                    </iframe>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Program Desa Section -->
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <p class="text-primary font-medium mb-2">Program Desa</p>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-800">Sekilas Informasi Mengenai Program Desa</h2>
        </div>

        <div class="grid md:grid-cols-2 gap-12 items-center">
            <!-- Image Section -->
            <div class="order-2 md:order-1">
                <div class="rounded-2xl overflow-hidden shadow-lg">
                    <img src="https://images.unsplash.com/photo-1574943320219-553eb213f72d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                        alt="Petani di sawah" class="w-full h-auto object-cover">
                </div>
            </div>

            <!-- Content Section -->
            <div class="order-1 md:order-2">
                <h3 class="text-2xl md:text-3xl font-bold text-gray-700 mb-8">
                    Sekilas Informasi Mengenai Program Desa.
                </h3>

                <div class="space-y-6">
                    <div class="flex items-start space-x-4">
                        <div class="w-3 h-3 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-1">Penyelenggaraan Pemerintahan Desa</h4>
                            <p class="text-gray-600 text-sm">Masyarakat Yang Adil dan Sejahtera</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="w-3 h-3 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-1">Pelaksanaan Pembangunan Desa</h4>
                            <p class="text-gray-600 text-sm">Masyarakat Yang Adil dan Sejahtera</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="w-3 h-3 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-1">Pembinaan Kemasyarakatan Desa</h4>
                            <p class="text-gray-600 text-sm">Masyarakat Yang Adil dan Sejahtera</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="w-3 h-3 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-1">Pemberdayaan Masyarakat Desa</h4>
                            <p class="text-gray-600 text-sm">Masyarakat Yang Adil dan Sejahtera</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="w-3 h-3 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-1">Penanggulangan Bencana</h4>
                            <p class="text-gray-600 text-sm">Masyarakat Yang Adil dan Sejahtera</p>
                        </div>
                    </div>
                </div>

                <div class="mt-8">
                    <a href="<?= base_url('pengelolaandana') ?>"
                        class="inline-block bg-primary-dark hover:bg-primary text-white px-8 py-3 rounded-full font-medium transition-colors duration-300">
                        Selengkapnya
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Data Infografis Section -->
<section class="py-16 bg-primary">
    <div class="container mx-auto px-4">
        <div class="text-left mb-12">
            <p class="text-white font-medium mb-2">Data Infografis</p>
            <h2 class="text-3xl md:text-4xl font-bold text-white">Infografis <?= isset($setting->desa) ? $setting->desa : 'Desa Kandangan Baru' ?></h2>
        </div>

        <div class="bg-white rounded-3xl p-8 md:p-12 shadow-xl">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12">
                <!-- Luas Kas -->
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-6 h-6 text-teal-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-gray-600 font-medium mb-1">Luas Kas</h3>
                        <p class="text-2xl md:text-3xl font-bold text-gray-800">
                            <?= isset($setting->luas_tanah_kas) ? number_format($setting->luas_tanah_kas) . 'm²' : '30.000m²' ?>
                        </p>
                    </div>
                </div>

                <!-- Luas Tanah -->
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-gray-600 font-medium mb-1">Luas Tanah</h3>
                        <p class="text-2xl md:text-3xl font-bold text-gray-800">
                            <?= isset($setting->luas_tanah_desa) ? number_format($setting->luas_tanah_desa) . 'm²' : '14.640.000m²' ?>
                        </p>
                    </div>
                </div>

                <!-- Luas DHKP -->
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-gray-600 font-medium mb-1">Luas DHKP</h3>
                        <p class="text-2xl md:text-3xl font-bold text-gray-800">
                            <?= isset($setting->luas_dhkp) ? number_format($setting->luas_dhkp) . 'm²' : '14.640.000m²' ?>
                        </p>
                    </div>
                </div>

                <!-- Jumlah Penduduk -->
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z">
                            </path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-gray-600 font-medium mb-1">Jumlah Penduduk</h3>
                        <p class="text-2xl md:text-3xl font-bold text-gray-800">
                            <?php
                            $total_penduduk = 0;
                            if (isset($infografis->laki) && isset($infografis->perempuan)) {
                                $total_penduduk = $infografis->laki + $infografis->perempuan;
                            }
                            echo $total_penduduk > 0 ? number_format($total_penduduk) . ' Jiwa' : '4.017 Jiwa';
                            ?>
                        </p>
                    </div>
                </div>

                <!-- Jumlah Laki-laki -->
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-6 h-6 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-gray-600 font-medium mb-1">Jumlah Laki-laki</h3>
                        <p class="text-2xl md:text-3xl font-bold text-gray-800">
                            <?= isset($infografis->laki) ? number_format($infografis->laki) : '3.125' ?>
                        </p>
                    </div>
                </div>

                <!-- Jumlah Perempuan -->
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-6 h-6 text-pink-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-gray-600 font-medium mb-1">Jumlah Perempuan</h3>
                        <p class="text-2xl md:text-3xl font-bold text-gray-800">
                            <?= isset($infografis->perempuan) ? number_format($infografis->perempuan) : '5.222' ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Marketplace Desa Section -->
<?php if (isset($produk) && count($produk) > 0) : ?>
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center mb-12">
                <div>
                    <p class="text-primary font-medium mb-2">Marketplace Desa</p>
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-800">Produk dan Kuliner</h2>
                </div>
                <div class="flex space-x-2">
                    <button
                        class="w-10 h-10 bg-primary hover:bg-primary-dark text-white rounded-full flex items-center justify-center transition-colors duration-300">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </button>
                    <button
                        class="w-10 h-10 bg-primary hover:bg-primary-dark text-white rounded-full flex items-center justify-center transition-colors duration-300">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <?php foreach (array_slice($produk, 0, 3) as $product) : ?>
                    <!-- Product Card -->
                    <div
                        class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                        <div class="relative">
                            <img src="<?= isset($product->foto) ? asset_url($product->foto) : 'https://images.unsplash.com/photo-1586201375761-83865001e31c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80' ?>"
                                alt="<?= $product->nama ?>" class="w-full h-64 object-cover">
                        </div>
                        <div class="p-6">
                            <div class="flex justify-between items-center mb-2">
                                <h3 class="text-lg font-semibold text-primary"><?= strlen($product->nama) > 20 ? substr($product->nama, 0, 20) . '...' : $product->nama ?></h3>
                                <button
                                    class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors duration-300">
                                    <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                            </div>
                            <p class="text-gray-600 text-sm mb-4"><?= isset($product->kategori) ? $product->kategori : 'Produk Desa' ?></p>
                            <a href="<?= base_url('guest/product_detail/' . $product->id) ?>"
                                class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-full text-sm font-medium transition-colors duration-300 inline-block">
                                Beli
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
<?php endif; ?>