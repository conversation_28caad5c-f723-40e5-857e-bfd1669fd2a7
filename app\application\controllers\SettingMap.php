<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property CI_DB_query_builder $db
 * @property VillagesBorder $villagesborder
 */
class SettingMap extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('VillagesBorder', 'villagesborder');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Setting Batas Wilayah';
        $data['content'] = 'settingmap/index';

        $border = $this->db->query("select a.username, b.nama_kelurahan, c.nama_kecamatan, d.nama_kabkota, e.border, e.custom_border from msusers a left join kelurahan b on b.id_kelurahan = a.kel<PERSON><PERSON>d left join kecamatan c ON c.id_kecamatan = b.id_kecamatan left join kabkota d on d.id_kabkota = c.id_kabkota left join villagesborder e on e.village = b.nama_kelurahan and e.sub_district = c.nama_kecamatan where a.id = '" . getCurrentIdUser() . "' order by c.nama_kecamatan asc")->row();

        if ($border->border != null || $border->custom_border != null) {
            if ($border->custom_border != null) {
                $border_decoded = json_decode($border->custom_border);
            } else {
                $border_decoded = json_decode($border->border);
            }

            $coordinates = array();
            foreach ($border_decoded as $key => $value) {
                $coordinates[] = array(
                    'lat' => (float)$value[1],
                    'lng' => (float)$value[0]
                );
            }

            $data['coords'] = json_encode($coordinates);
            if (isset($coordinates[0]['lat']) && isset($coordinates[0]['lng']) && $coordinates[0]['lat'] != null && $coordinates[0]['lng'] != null) {
                $data['firstcoords'] = json_encode(array(
                    'lat' => $coordinates[0]['lat'] - 0.02,
                    'lng' => $coordinates[0]['lng'] + 0.03
                ));
            } else {
                $data['firstcoords'] = null;
            }
        } else {
            $data['coords'] = null;
        }

        return $this->load->view('master', $data);
    }

    public function process_update()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $coords = getPost('coords', []);

        if (count($coords) == 0) {
            return JSONResponseDefault('FAILED', 'Batas wilayah tidak boleh kosong');
        }

        $coords_store = array();
        foreach ($coords as $key => $value) {
            $coords_store[] = array($value['lng'], $value['lat']);
        }

        $border = $this->db->query("SELECT e.id AS borderid, f.nama_provinsi, a.username, b.nama_kelurahan, c.nama_kecamatan, d.nama_kabkota, e.border FROM msusers a JOIN kelurahan b ON b.id_kelurahan = a.kelurahanid JOIN kecamatan c ON c.id_kecamatan = b.id_kecamatan JOIN kabkota d ON d.id_kabkota = c.id_kabkota LEFT JOIN villagesborder e ON e.village = b.nama_kelurahan AND e.sub_district = c.nama_kecamatan JOIN provinsi f ON f.id_provinsi = d.id_propinsi WHERE a.id = '" . getCurrentIdUser() . "' ORDER BY c.nama_kecamatan ASC")->row();

        $execute = array();
        $execute['province'] = $border->nama_provinsi;
        $execute['district'] = $border->nama_kabkota;
        $execute['sub_district'] = $border->nama_kecamatan;
        $execute['village'] = $border->nama_kelurahan;
        $execute['custom_border'] = json_encode($coords_store);

        if ($border->border == null) {
            $execute['border'] = json_encode($coords_store);
        }

        if ($border->borderid == null) {
            $this->villagesborder->insert($execute);
        } else {
            $this->villagesborder->update(array(
                'id' => $border->borderid
            ), $execute);
        }

        return JSONResponseDefault('OK', 'Batas wilayah berhasil diperbarui');
    }
}
