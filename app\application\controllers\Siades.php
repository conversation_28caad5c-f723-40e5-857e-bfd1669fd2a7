<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Master_Users $msusers
 */
class Siades extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Master_Users', 'msusers');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Sistem Absensi Desa';
        $data['content'] = 'siades/index';
        $data['currentuser'] = getCurrentUser();

        return $this->load->view('master', $data);
    }

    public function process()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Silahkan login terlebih dahulu');
        }

        $username = getPost('username');
        $api_key_siades = getPost('api_key_siades');
        $sync_siades = getPost('sync_siades');

        if ($username == null) {
            return JSONResponseDefault('FAILED', 'Username tidak boleh kosong');
        }

        if ($api_key_siades == null) {
            return JSONResponseDefault('FAILED', 'API Key tidak boleh kosong');
        }

        $get = $this->msusers->get(array(
            'a.username_siades' => $username,
            'a.id !=' => getCurrentIdUser()
        ));

        if ($get->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Username sudah digunakan');
        }

        $update = array();
        $update['username_siades'] = $username;
        $update['api_key_siades'] = $api_key_siades;
        $update['sync_siades'] = $sync_siades;

        $this->msusers->update(array(
            'id' => getCurrentIdUser()
        ), $update);

        return JSONResponseDefault('OK', 'Konfigurasi berhasil disimpan');
    }

    public function export_attendance_recap()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Silahkan login terlebih dahulu');
        }

        $month = getPost('month');
        $format = getPost('format');
        $type = getPost('type');

        if ($month == null) {
            return JSONResponseDefault('FAILED', 'Bulan tidak boleh kosong');
        }

        if ($format == null) {
            return JSONResponseDefault('FAILED', 'Format tidak boleh kosong');
        }

        if ($type == null) {
            return JSONResponseDefault('FAILED', 'Type tidak boleh kosong');
        }

        // Validasi format
        if (!in_array($format, ['pdf', 'excel'])) {
            return JSONResponseDefault('FAILED', 'Format harus pdf atau excel');
        }

        // Validasi type
        if (!in_array($type, ['BPD', 'Aparat Desa', 'All'])) {
            return JSONResponseDefault('FAILED', 'Type harus BPD, Aparat Desa, atau All');
        }

        // Ambil API key SIADES
        $api_key = get_api_key_siades();

        if (empty($api_key)) {
            return JSONResponseDefault('FAILED', 'API Key SIADES tidak ditemukan');
        }

        // Request ke API SIADES
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://siades.id/api/thirdparty/export/attendance/recap',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => http_build_query(array(
                'apikey' => $api_key,
                'month' => $month,
                'format' => $format,
                'type' => $type
            )),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded',
            ),
        ));

        $response = curl_exec($curl);
        $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        if ($response === false) {
            return JSONResponseDefault('FAILED', 'Gagal menghubungi server SIADES');
        }

        $result = json_decode($response, true);

        if ($http_code !== 200) {
            return JSONResponseDefault('FAILED', 'Server SIADES mengembalikan error: ' . $http_code);
        }

        if (!$result || !isset($result['status'])) {
            return JSONResponseDefault('FAILED', 'Response tidak valid dari server SIADES');
        }

        if ($result['status'] !== true) {
            $message = isset($result['message']) ? $result['message'] : 'Gagal menggenerate laporan';
            return JSONResponseDefault('FAILED', $message);
        }

        if (!isset($result['data']['download_url'])) {
            return JSONResponseDefault('FAILED', 'URL download tidak ditemukan dalam response');
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'MESSAGE' => 'Laporan berhasil digenerate',
            'DOWNLOAD_URL' => $result['data']['download_url'],
            'FILENAME' => isset($result['data']['filename']) ? $result['data']['filename'] : 'laporan_absensi.pdf',
            'FILE_SIZE' => isset($result['data']['file_size']) ? $result['data']['file_size'] : 0
        ));
    }

    public function get_attendance_recap()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Silahkan login terlebih dahulu');
        }

        $month = getPost('month_view');
        $type = getPost('type_view');

        if ($month == null) {
            return JSONResponseDefault('FAILED', 'Bulan tidak boleh kosong');
        }

        if ($type == null) {
            return JSONResponseDefault('FAILED', 'Type tidak boleh kosong');
        }

        // Validasi type
        if (!in_array($type, ['BPD', 'Aparat Desa', 'All'])) {
            return JSONResponseDefault('FAILED', 'Type harus BPD, Aparat Desa, atau All');
        }

        // Ambil API key SIADES
        $api_key = get_api_key_siades();

        if (empty($api_key)) {
            return JSONResponseDefault('FAILED', 'API Key SIADES tidak ditemukan');
        }

        // Request ke API SIADES
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://siades.id/api/thirdparty/get/attendance/recap',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => http_build_query(array(
                'apikey' => $api_key,
                'month' => $month,
                'type' => $type
            )),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded',
            ),
        ));

        $response = curl_exec($curl);
        $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        if ($response === false) {
            return JSONResponseDefault('FAILED', 'Gagal menghubungi server SIADES');
        }

        $result = json_decode($response, true);

        if ($http_code !== 200) {
            return JSONResponseDefault('FAILED', 'Server SIADES mengembalikan error: ' . $http_code);
        }

        if (!$result || !isset($result['status'])) {
            return JSONResponseDefault('FAILED', 'Response tidak valid dari server SIADES');
        }

        if ($result['status'] !== true) {
            $message = isset($result['message']) ? $result['message'] : 'Gagal mengambil data';
            return JSONResponseDefault('FAILED', $message);
        }

        if (!isset($result['data'])) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan dalam response');
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'MESSAGE' => 'Data berhasil diambil',
            'DATA' => $result['data'],
            'METADATA' => isset($result['metadata']) ? $result['metadata'] : null
        ));
    }
}
