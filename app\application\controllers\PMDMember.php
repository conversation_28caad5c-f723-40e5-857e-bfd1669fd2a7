<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property PMD_Member $pmdmember
 */
class PMDMember extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('PMD_Member', 'pmdmember');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isPMD()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Anggota PMD';
        $data['content'] = 'pmdmember/index';
        $data['pmdmember'] = $this->pmdmember->result();

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isPMD()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Anggota PMD';
        $data['content'] = 'pmdmember/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Silahkan login terlebih dahulu');
        } else if (!isPMD()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $nama = getPost('nama');
        $jabatan = getPost('jabatan');
        $phonenumber = getPost('phonenumber');

        $insert = array();
        $insert['name'] = $nama;
        $insert['phonenumber'] = $phonenumber;
        $insert['position'] = $jabatan;
        $insert['userid'] = getCurrentIdUser();
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();
        $insert['position'] = $jabatan;

        $this->pmdmember->insert($insert);

        return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Silahkan login terlebih dahulu');
        } else if (!isPMD()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->pmdmember->get(array(
            'id' => $id,
            'userid' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $this->pmdmember->delete(array(
            'id' => $id,
            'userid' => getCurrentIdUser()
        ));

        return JSONResponseDefault('OK', 'Data berhasil dihapus');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isPMD()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->pmdmember->get(array(
            'id' => $id,
            'userid' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('pmd/member'));
        }

        $data = array();
        $data['title'] = 'Edit Anggota PMD';
        $data['content'] = 'pmdmember/edit';
        $data['pmdmember'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isPMD()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->pmdmember->get(array(
            'id' => $id,
            'userid' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $nama = getPost('nama');
        $jabatan = getPost('jabatan');
        $phonenumber = getPost('phonenumber');

        $update = array();
        $update['name'] = $nama;
        $update['phonenumber'] = $phonenumber;
        $update['position'] = $jabatan;
        $update['userid'] = getCurrentIdUser();
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->pmdmember->update(array(
            'id' => $id,
            'userid' => getCurrentIdUser()
        ), $update);

        return JSONResponseDefault('OK', 'Data berhasil diubah');
    }
}
