<?php

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Warga_Model warga
 * @property Setting_Umum settingumum
 * @property Master_Users msusers
 * @property Infografis_Model infografis
 * @property CI_DB_mysqli_driver $db
 * @property CI_Upload $upload
 * @property Datatables $datatables
 */
class Warga extends MY_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->load->model('Warga_Model', 'warga');
		$this->load->model('Setting_Umum', 'settingumum');
		$this->load->model('Master_Users', 'msusers');
		$this->load->model('Infografis_Model', 'infografis');
	}

	public function index()
	{
		$datasource = getGet('datasource');
		$status = getGet('status');

		if (!isLogin()) {
			return redirect('auth/login');
		} else if (!isAdmin()) {
			return redirect(base_url('dashboard'));
		}

		$where = array();
		if (getSessionValue('ROLE') == 'admin') {
			$where['id_user'] = getCurrentIdUser();
		}

		$data['title'] = 'Warga';
		$data['content'] = 'warga/index';
		$data['setting'] = $this->settingumum->getDefaultData($where)->row();
		$data['current_filterstatus'] = $status;
		$data['current_datasource'] = $datasource;

		return $this->load->view('master', $data);
	}

	public function add()
	{
		if (!isLogin()) {
			return redirect('auth/login');
		} else if (!isAdmin()) {
			return redirect(base_url('dashboard'));
		}

		$data['title'] = 'Warga - Add';
		$data['content'] = 'warga/add';

		return $this->load->view('master', $data);
	}

	public function api_create()
	{
		$nik  = getPost('nik');
		$nomor_kk = getPost('nomor_kk');
		$nama = getPost('nama');
		$alamat = getPost('alamat');
		$rt = getPost('rt');
		$rw = getPost('rw');
		$kode_pos = getPost('kode_pos');
		$jenkel = getPost('jenis_kelamin');
		$tempat_lahir = getPost('tempat_lahir');
		$tanggal_lahir = getPost('tanggal_lahir');
		$agama = getPost('agama');
		$pendidikan = getPost('pendidikan');
		$pekerjaan = getPost('pekerjaan');
		$statuskawin = getPost('status_perkawinan');
		$statuskeluarga = getPost('status_hubungan_dalam_keluarga');
		$desaid = getPost('desaid');

		if ($nik == null) {
			return JSONResponseDefault('FAILED', 'NIK tidak boleh kosong');
		}

		if ($nomor_kk == null) {
			return JSONResponseDefault('FAILED', 'Nomor KK tidak boleh kosong');
		}

		if ($nama == null) {
			return JSONResponseDefault('FAILED', 'Nama tidak boleh kosong');
		}

		if ($alamat == null) {
			return JSONResponseDefault('FAILED', 'Alamat tidak boleh kosong');
		}

		if ($rt == null) {
			return JSONResponseDefault('FAILED', 'RT tidak boleh kosong');
		}

		if ($rw == null) {
			return JSONResponseDefault('FAILED', 'RW tidak boleh kosong');
		}

		if ($kode_pos == null) {
			return JSONResponseDefault('FAILED', 'Kode Pos tidak boleh kosong');
		}

		if ($jenkel == null) {
			return JSONResponseDefault('FAILED', 'Jenis Kelamin tidak boleh kosong');
		}

		if ($tempat_lahir == null) {
			return JSONResponseDefault('FAILED', 'Tempat Lahir tidak boleh kosong');
		}

		if ($tanggal_lahir == null) {
			return JSONResponseDefault('FAILED', 'Tanggal Lahir tidak boleh kosong');
		}

		if ($agama == null) {
			return JSONResponseDefault('FAILED', 'Agama tidak boleh kosong');
		}

		if ($pendidikan == null) {
			return JSONResponseDefault('FAILED', 'Pendidikan tidak boleh kosong');
		}

		if ($pekerjaan == null) {
			return JSONResponseDefault('FAILED', 'Pekerjaan tidak boleh kosong');
		}

		if ($statuskawin == null) {
			return JSONResponseDefault('FAILED', 'Status Perkawinan tidak boleh kosong');
		}

		if ($statuskeluarga == null) {
			return JSONResponseDefault('FAILED', 'Status Hubungan Dalam Keluarga tidak boleh kosong');
		}

		if ($desaid == null) {
			return JSONResponseDefault('FAILED', 'Desa tidak boleh kosong');
		}

		$getdesa = $this->msusers->select('b.id_kelurahan, b.id_kecamatan, b.id_kabkota')
			->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
			->getDefaultData(array(
				'id' => $desaid
			));

		if ($getdesa->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Desa tidak ditemukan');
		}

		$row = $getdesa->row();

		$validate = $this->warga->getDefaultData(array(
			'a.nik' => $nik
		));

		if ($validate->num_rows() > 0) {
			return JSONResponseDefault('FAILED', 'NIK yang anda masukkan telah terdaftar');
		}

		$insert = array();
		$insert['nik'] = $nik;
		$insert['nomor_kk'] = $nomor_kk;
		$insert['nama'] = $nama;
		$insert['alamat'] = $alamat;
		$insert['rt'] = $rt;
		$insert['rw'] = $rw;
		$insert['kode_pos'] = $kode_pos;
		$insert['jenis_kelamin'] = $jenkel;
		$insert['tempat_lahir'] = $tempat_lahir;
		$insert['tanggal_lahir'] = $tanggal_lahir;
		$insert['agama'] = strtoupper($agama);
		$insert['pendidikan'] = $pendidikan;
		$insert['pekerjaan'] = $pekerjaan;
		$insert['status_perkawinan'] = $statuskawin;
		$insert['status_hubungan_dalam_keluarga'] = $statuskeluarga;
		$insert['id_kabkota'] = $row->id_kabkota;
		$insert['id_kecamatan'] = $row->id_kecamatan;
		$insert['id_kelurahan'] = $row->id_kelurahan;
		$insert['id_user'] = $desaid;

		$doInsert = $this->warga->insert($insert);

		if ($doInsert) {
			return JSONResponseDefault('OK', 'Warga berhasil ditambahkan');
		} else {
			return JSONResponseDefault('FAILED', 'Gagal menambahkan warga');
		}
	}

	public function process_add()
	{
		if (!isPostAjax()) {
			return JSONResponseRequestReject();
		} else if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Session required');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Access denied');
		}

		$nik  = getPost('nik');
		$nomor_kk = getPost('nomor_kk');
		$nama = getPost('nama');
		$alamat = getPost('alamat');
		$rt = getPost('rt');
		$rw = getPost('rw');
		$kode_pos = getPost('kode_pos');
		$jenkel = getPost('jenis_kelamin');
		$tempat_lahir = getPost('tempat_lahir');
		$tanggal_lahir = getPost('tanggal_lahir');
		$agama = getPost('agama');
		$pendidikan = getPost('pendidikan');
		$pekerjaan = getPost('pekerjaan');
		$statuskawin = getPost('status_perkawinan');
		$statuskeluarga = getPost('status_hubungan_dalam_keluarga');
		$golongan_darah = getPost('golongandarah');
		$rfid = getPost('rfid');
		$status = getPost('status');
		$kewarganegaraan = getPost('kewarganegaraan');
		$akseptorkb = getPost('akseptorkb');

		$getdesa = $this->msusers->select('b.id_kelurahan, b.id_kecamatan, b.id_kabkota')
			->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
			->getDefaultData(array(
				'id' => getCurrentIdUser()
			));

		if ($getdesa->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Desa tidak ditemukan');
		}

		$row = $getdesa->row();

		$validate = $this->warga->getDefaultData(array(
			'a.nik' => $nik
		));

		if ($validate->num_rows() > 0) {
			return JSONResponseDefault('FAILED', 'NIK yang anda masukkan telah terdaftar');
		}

		$insert = array();
		$insert['nik'] = $nik;
		$insert['nomor_kk'] = $nomor_kk;
		$insert['nama'] = $nama;
		$insert['alamat'] = $alamat;
		$insert['rt'] = $rt;
		$insert['rw'] = $rw;
		$insert['kode_pos'] = $kode_pos;
		$insert['jenis_kelamin'] = $jenkel;
		$insert['tempat_lahir'] = $tempat_lahir;
		$insert['tanggal_lahir'] = $tanggal_lahir;
		$insert['agama'] = strtoupper($agama);
		$insert['pendidikan'] = $pendidikan;
		$insert['pekerjaan'] = $pekerjaan;
		$insert['status_perkawinan'] = $statuskawin;
		$insert['status_hubungan_dalam_keluarga'] = $statuskeluarga;
		$insert['id_kabkota'] = $row->id_kabkota;
		$insert['id_kecamatan'] = $row->id_kecamatan;
		$insert['id_kelurahan'] = $row->id_kelurahan;
		$insert['id_user'] = getCurrentIdUser();
		$insert['rfid'] = str_replace(' ', '', $rfid ?? '');
		$insert['status'] = $status;
		$insert['golongan_darah'] = $golongan_darah;
		$insert['kewarganegaraan'] = $kewarganegaraan;
		$insert['akseptor_kb'] = $akseptorkb;

		$doInsert = $this->warga->insert($insert);

		if ($doInsert) {
			return JSONResponseDefault('OK', 'Warga berhasil ditambahkan');
		} else {
			return JSONResponseDefault('FAILED', 'Gagal menambahkan warga');
		}
	}

	public function edit($id)
	{
		if (!isLogin()) {
			return redirect('auth/login');
		} else if (!isAdmin()) {
			return redirect('dashboard');
		}

		$get = $this->warga->getDataJoin(array(
			'warga.nik' => $id,
			'warga.id_user' => getCurrentIdUser(),
		));

		if ($get->num_rows() == 0) {
			return redirect('warga');
		}

		$data['title'] = 'Warga - Edit';
		$data['content'] = 'warga/edit';
		$data['warga'] = $get->row();

		return $this->load->view('master', $data);
	}

	public function process_edit($id)
	{
		if (!isPostAjax()) {
			return JSONResponseRequestReject();
		} else if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Session required');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Access denied');
		}

		$get = $this->warga->getDataJoin(array(
			'warga.nik' => $id,
			'warga.id_user' => getCurrentIdUser(),
		));

		if ($get->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
		}

		$getdesa = $this->msusers->select('b.id_kelurahan, b.id_kecamatan, b.id_kabkota')
			->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
			->getDefaultData(array(
				'id' => getCurrentIdUser()
			));

		if ($getdesa->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Desa tidak ditemukan');
		}

		$row = $getdesa->row();

		$nik = getPost('nik');
		$nomor_kk = getPost('nomor_kk');
		$nama = getPost('nama');
		$alamat = getPost('alamat');
		$rt = getPost('rt');
		$rw = getPost('rw');
		$kode_pos = getPost('kode_pos');
		$jenkel = getPost('jenis_kelamin');
		$tempat_lahir = getPost('tempat_lahir');
		$tanggal_lahir = getPost('tanggal_lahir');
		$agama = getPost('agama');
		$pendidikan = getPost('pendidikan');
		$pekerjaan = getPost('pekerjaan');
		$statuskawin = getPost('status_perkawinan');
		$statuskeluarga = getPost('status_hubungan_dalam_keluarga');
		$golongan_darah = getPost('golongandarah');
		$rfid = getPost('rfid');
		$status = getPost('status');
		$kewarganegaraan = getPost('kewarganegaraan');
		$akseptorkb = getPost('akseptorkb');

		if ($id != $nik) {
			$validate = $this->warga->getDefaultData(array(
				'a.nik' => $nik
			));

			if ($validate->num_rows() > 0) {
				return JSONResponseDefault('FAILED', 'NIK yang anda masukkan telah terdaftar');
			}
		}

		$update = array();
		$update['nik'] = $nik;
		$update['nomor_kk'] = $nomor_kk;
		$update['nama'] = $nama;
		$update['alamat'] = $alamat;
		$update['rt'] = $rt;
		$update['rw'] = $rw;
		$update['kode_pos'] = $kode_pos;
		$update['jenis_kelamin'] = $jenkel;
		$update['tempat_lahir'] = $tempat_lahir;
		$update['tanggal_lahir'] = $tanggal_lahir;
		$update['agama'] = strtoupper($agama);
		$update['pendidikan'] = $pendidikan;
		$update['pekerjaan'] = $pekerjaan;
		$update['status_perkawinan'] = $statuskawin;
		$update['status_hubungan_dalam_keluarga'] = $statuskeluarga;
		$update['id_kabkota'] = $row->id_kabkota;
		$update['id_kecamatan'] = $row->id_kecamatan;
		$update['id_kelurahan'] = $row->id_kelurahan;
		$update['rfid'] = str_replace(' ', '', $rfid ?? '');
		$update['status'] = $status;
		$update['golongan_darah'] = $golongan_darah;
		$update['kewarganegaraan'] = $kewarganegaraan;
		$update['akseptor_kb'] = $akseptorkb;

		$doUpdate = $this->warga->update(array(
			'nik' => $id
		), $update);

		if ($doUpdate) {
			return JSONResponseDefault('OK', 'Data berhasil diubah');
		} else {
			return JSONResponseDefault('FAILED', 'Gagal mengubah data');
		}
	}

	public function process_delete()
	{
		if (!isPostAjax()) {
			return JSONResponseRequestReject();
		} else if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Session required');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Access denied');
		}

		$id = getPost('id');

		$get = $this->warga->getDefaultData(array(
			'a.nik' => $id,
			'a.id_user' => getCurrentIdUser(),
		));

		if ($get->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
		}

		$delete = $this->warga->delete(array(
			'nik' => $id
		));

		if ($delete) {
			return JSONResponseDefault('OK', 'Data berhasil dihapus');
		} else {
			return JSONResponseDefault('FAILED', 'Gagal menghapus data');
		}
	}

	public function detail()
	{
		if (!isPostAjax()) {
			return JSONResponseRequestReject();
		} else if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Session required');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Access denied');
		}

		$id = getPost('id');

		$warga = $this->warga->getDataJoin(array(
			'warga.nik' => $id,
			'warga.id_user' => getCurrentIdUser()
		));

		if ($warga->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Data not found');
		}

		$data = array();
		$data['keluarga'] = $this->warga->getDefaultData(array(
			'a.nomor_kk' => $warga->row()->nomor_kk
		));
		$data['warga'] = $warga->row();

		return JSONResponse(array(
			'RESULT' => 'OK',
			'CONTENT' => $this->load->view('warga/detail', $data, true)
		));
	}

	public function api_get()
	{
		$desaid = getGet('desaid');

		$warga = $this->warga->getDefaultData(array(
			'id_user' => $desaid
		))->result();

		return JSONResponse($warga);
	}

	public function api_sync()
	{
		try {
			$this->db->trans_begin();

			$json = getPost('json');
			$json = json_decode($json);
			$desaid = getPost('desaid');

			$getdesa = $this->msusers->select('b.id_kelurahan, b.id_kecamatan, b.id_kabkota')
				->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
				->getDefaultData(array(
					'id' => $desaid
				));

			if ($getdesa->num_rows() == 0) {
				return JSONResponseDefault('FAILED', 'Desa tidak ditemukan');
			}

			$row = $getdesa->row();

			foreach ($json as $key => $value) {
				$warga = $this->warga->getDefaultData(array(
					'nik' => $value->nik
				));

				if ($warga->num_rows() == 0) {
					$insert = array();
					$insert['nik'] = $value->nik;
					$insert['nomor_kk'] = $value->kk;
					$insert['nama'] = $value->nama;
					$insert['alamat'] = $value->alamat;
					$insert['rt'] = $value->rt;
					$insert['rw'] = $value->rw;
					$insert['kode_pos'] = $value->kodepos;
					$insert['jenis_kelamin'] = $value->jenis_kelamin;
					$insert['tempat_lahir'] = $value->lahir;
					$insert['tanggal_lahir'] = $value->tanggallahir;
					$insert['agama'] = strtoupper($value->agama);
					$insert['pendidikan'] = $value->pendidikan;
					$insert['status_perkawinan'] = $value->status_perkawinan;
					$insert['status_hubungan_dalam_keluarga'] = $value->status_hubungan;
					$insert['id_kabkota'] = $row->id_kabkota;
					$insert['id_kecamatan'] = $row->id_kecamatan;
					$insert['id_kelurahan'] = $row->id_kelurahan;
					$insert['id_user'] = $desaid;

					$this->warga->insert($insert);
				}
			}

			if ($this->db->trans_status() == TRUE) {
				$this->db->trans_commit();

				return JSONResponseDefault('OK', 'Data berhasil disinkronkan');
			} else {
				$this->db->trans_rollback();

				return JSONResponseDefault('FAILED', 'Gagal melakukan sinkron data');
			}
		} catch (Exception $e) {
			return JSONResponseDefault('FAILED', 'Data gagal disinkronkan');
		}
	}

	public function importexcel()
	{
		try {
			ini_set('max_execution_time', 0);

			if (!isPostAjax()) {
				return JSONResponseRequestReject();
			} else if (!isLogin()) {
				return JSONResponseDefault('FAILED', 'Session required');
			} else if (!isAdmin()) {
				return JSONResponseDefault('FAILED', 'Access denied');
			}

			if (isset($_FILES["file"]["name"])) {
				$this->db->trans_begin();

				$file_tmp = $_FILES['file']['tmp_name'];

				$object = PHPExcel_IOFactory::load($file_tmp);

				$getdesa = $this->msusers->select('b.id_kelurahan, b.id_kecamatan, b.id_kabkota')
					->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
					->getDefaultData(array(
						'id' => getCurrentIdUser()
					));

				$row_user = $getdesa->row();
				if ($getdesa->num_rows() > 0) {
					$kota = $row_user->id_kabkota;
					$kelurahan = $row_user->id_kelurahan;
					$kecamatan = $row_user->id_kecamatan;
				} else {
					return JSONResponseDefault('FAILED', 'Akun anda belum dikonfigurasi oleh super admin untuk memberikan cluster kelurahan, Silahkan hubungi admin');
				}

				$lakilaki = 0;
				$perempuan = 0;
				$tidaksekolah = 0;
				$belumtamat = 0;
				$tamatsd = 0;
				$sltp = 0;
				$slta = 0;
				$diploma1 = 0;
				$diploma3 = 0;
				$diploma4 = 0;
				$art = 0;
				$tidakbekerja = 0;
				$pelajar = 0;
				$wiraswasta = 0;
				$buruh = 0;
				$pns = 0;
				$swasta = 0;

				foreach ($object->getWorksheetIterator() as $worksheet) {
					$highestRow = $worksheet->getHighestRow();

					for ($row = 2; $row <= $highestRow; $row++) {
						$nik = $worksheet->getCellByColumnAndRow(0, $row)->getValue();
						$nomorkk = $worksheet->getCellByColumnAndRow(1, $row)->getValue();

						// remove non-numeric characters from nomorkk
						$nomorkk = preg_replace('/\D/', '', $nomorkk);

						$nama = $worksheet->getCellByColumnAndRow(2, $row)->getValue();
						$alamat = $worksheet->getCellByColumnAndRow(3, $row)->getValue();
						$rt = $worksheet->getCellByColumnAndRow(4, $row)->getValue();
						$rw = $worksheet->getCellByColumnAndRow(5, $row)->getValue();
						$kodepos = $worksheet->getCellByColumnAndRow(6, $row)->getValue();
						$jenkel = $worksheet->getCellByColumnAndRow(7, $row)->getValue();
						$tempatlahir = $worksheet->getCellByColumnAndRow(8, $row)->getValue();
						$tanggallahir = $worksheet->getCellByColumnAndRow(9, $row);

						try {
							if (PHPExcel_Shared_Date::isDateTime($tanggallahir)) {
								$tanggallahir = date('Y-m-d', PHPExcel_Shared_Date::ExcelToPHP($tanggallahir->getValue()));
							} else {
								$tanggallahir = $tanggallahir->getValue();
							}
						} catch (Exception $e) {
							log_message('error', 'Error on line ' . $e->getLine() . ' in file ' . $e->getFile() . ' with message ' . $e->getMessage());
							$tanggallahir = $tanggallahir->getValue();
						}

						$tanggallahir = date('Y-m-d', strtotime($tanggallahir));
						$tanggallahir = PHPExcel_Style_NumberFormat::toFormattedString($tanggallahir, 'YYYY-MM-DD');
						$agama = $worksheet->getCellByColumnAndRow(10, $row)->getValue();
						$pendidikan = $worksheet->getCellByColumnAndRow(11, $row)->getValue();
						$pekerjaan = $worksheet->getCellByColumnAndRow(12, $row)->getValue();
						$statuskawin = $worksheet->getCellByColumnAndRow(13, $row)->getValue();
						$statushubungan = $worksheet->getCellByColumnAndRow(14, $row)->getValue();

						if (empty($nik) || empty($nama)) {
							continue;
						}

						if (!is_numeric($nik)) {
							continue;
						}

						if (strlen($nik) != 16) {
							continue;
						}

						if (in_array($jenkel, array('L', 'LK'))) {
							$jenkel = 'Laki-laki';
							$lakilaki += 1;
						} else if (in_array($jenkel, array('P', 'PR'))) {
							$jenkel = 'Perempuan';
							$perempuan += 1;
						} else {
							$jenkel = 'Laki-laki';
							$lakilaki += 1;
						}

						if ($pendidikan == 'Tidak/Belum Sekolah') {
							$tidaksekolah += 1;
						} else if ($pendidikan == 'Tidak Tamat SD/Sederajat') {
							$belumtamat += 1;
						} else if ($pendidikan == 'Tamat SD/Sederajat') {
							$tamatsd += 1;
						} else if ($pendidikan == 'SLTP/Sederajat') {
							$sltp += 1;
						} else if ($pendidikan == 'SLTA/Sederajat') {
							$slta += 1;
						} else if ($pendidikan == 'Diploma I/II') {
							$diploma1 += 1;
						} else if ($pendidikan == 'Diploma III/Sarjana Muda') {
							$diploma3 += 1;
						} else if ($pendidikan == 'Diploma IV/Starta 1') {
							$diploma4 += 1;
						}

						if ($pekerjaan == 'Mengurus Rumah Tangga') {
							$art += 1;
						} else if ($pekerjaan == 'Belum/Tidak Bekerja') {
							$tidakbekerja += 1;
						} else if ($pekerjaan == 'Pelajar/Mahasiswa') {
							$pelajar += 1;
						} else if ($pekerjaan == 'Wiraswasta') {
							$wiraswasta += 1;
						} else if ($pekerjaan == 'Buruh Harian Lepas') {
							$buruh += 1;
						} else if ($pekerjaan == 'Pegawai Negeri Sipil') {
							$pns += 1;
						} else if ($pekerjaan == 'Karyawan Swasta') {
							$swasta += 1;
						}

						$get = $this->warga->getDefaultData(array(
							'a.nik' => $nik
						));

						if ($get->num_rows() != 0) {
							$update = array();
							$update['nik'] = $nik;
							$update['nomor_kk'] = $nomorkk;
							$update['nama'] = $nama;
							$update['alamat'] = $alamat;
							$update['rt'] = $rt;
							$update['rw'] = $rw;
							$update['kode_pos'] = $kodepos;
							$update['jenis_kelamin'] = $jenkel;
							$update['tempat_lahir'] = $tempatlahir;
							$update['tanggal_lahir'] = $tanggallahir;
							$update['agama'] = $agama;
							$update['pendidikan'] = $pendidikan;
							$update['pekerjaan'] = $pekerjaan;
							$update['status_perkawinan'] = $statuskawin;
							$update['status_hubungan_dalam_keluarga'] = $statushubungan;
							$update['id_kabkota'] = $kota;
							$update['id_kecamatan'] = $kecamatan;
							$update['id_kelurahan'] = $kelurahan;
							$update['id_user'] = getCurrentIdUser();

							$update = $this->warga->update(array('nik' => $nik), $update);
						} else {
							$insert = array();

							if (empty($kodepos)) {
								$kodepos = 0;
							}

							$insert = array(
								'nik' => $nik,
								'nomor_kk' => $nomorkk,
								'nama' => $nama,
								'alamat' => $alamat,
								'rt' => $rt,
								'rw' => $rw,
								'kode_pos' => $kodepos,
								'jenis_kelamin' => $jenkel,
								'tempat_lahir' => $tempatlahir,
								'tanggal_lahir' => $tanggallahir,
								'agama' => strtoupper($agama),
								'pendidikan' => $pendidikan,
								'pekerjaan' => $pekerjaan,
								'status_perkawinan' => $statuskawin,
								'status_hubungan_dalam_keluarga' => $statushubungan,
								'id_kabkota' => $kota,
								'id_kecamatan' => $kecamatan,
								'id_kelurahan' => $kelurahan,
								'id_user' => getCurrentIdUser()
							);

							$this->warga->insert($insert);
						}
					}
				}

				$get = $this->infografis->get(array('id_user' => getCurrentIdUser()));

				if ($get->num_rows() == 0) {
					$insert = array();
					$insert['laki'] = $lakilaki;
					$insert['perempuan'] = $perempuan;
					$insert['sd'] = $tamatsd;
					$insert['sltp'] = $sltp;
					$insert['belum_sekolah'] = $belumtamat;
					$insert['slta'] = $slta;
					$insert['belum_tamat'] = $belumtamat;
					$insert['dsi'] = $diploma4;
					$insert['d1'] = $diploma1;
					$insert['d2'] = $diploma1;
					$insert['d3'] = $diploma3;
					$insert['pengganguran'] = $tidakbekerja;
					$insert['ibu_rt'] = $art;
					$insert['pelajar'] = $pelajar;
					$insert['pns'] = $pns;
					$insert['karyawan_swasta'] = $swasta;
					$insert['wiraswasta'] = $wiraswasta;
					$insert['buruh_harian'] = $buruh;
					$insert['id_user'] = getCurrentIdUser();

					$this->infografis->insert($insert);
				} else {
					$row = $get->row();

					$update = array();
					$update['laki'] = $row->laki + $lakilaki;
					$update['perempuan'] = $row->perempuan + $perempuan;
					$update['sd'] = $row->sd + $tamatsd;
					$update['sltp'] = $row->sltp + $sltp;
					$update['belum_sekolah'] = $row->belum_sekolah + $belumtamat;
					$update['slta'] = $row->slta + $slta;
					$update['belum_tamat'] = $row->belum_tamat + $belumtamat;
					$update['dsi'] = $row->dsi + $diploma4;
					$update['d1'] = $row->d1 + $diploma1;
					$update['d2'] = $row->d2 + $diploma1;
					$update['d3'] = $row->d3 + $diploma3;
					$update['pengganguran'] = $row->pengganguran + $tidakbekerja;
					$update['ibu_rt'] = $row->ibu_rt + $art;
					$update['pelajar'] = $row->pelajar + $pelajar;
					$update['pns'] = $row->pns + $pns;
					$update['karyawan_swasta'] = $row->karyawan_swasta + $swasta;
					$update['wiraswasta'] = $row->wiraswasta + $wiraswasta;
					$update['buruh_harian'] = $row->buruh_harian + $buruh;

					$this->infografis->update(array('id' => $row->id), $update);
				}

				if ($this->db->trans_status() === FALSE) {
					$this->db->trans_rollback();

					return JSONResponseDefault('FAILED', 'Gagal menambahkan data');
				}

				$this->db->trans_commit();

				return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
			} else {
				return JSONResponseDefault('FAILED', 'Gagal menambahkan data');
			}
		} catch (Exception $ex) {
			$this->db->trans_rollback();

			return JSONResponseDefault('FAILED', $ex->getMessage());
		}
	}

	public function format_excel()
	{
		$this->load->helper('download');
		return force_download('./assets/format_excel/Import Data Warga.xlsx', null);
	}

	public function rfid()
	{
		if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Anda belum login');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
		}

		$nik = getPost('nik');

		$warga = $this->warga->get(array(
			'a.nik' => $nik
		));

		if ($warga->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
		}

		$row = $warga->row();

		$data = array();
		$data['warga'] = $row;

		return JSONResponse(array(
			'RESULT' => 'OK',
			'CONTENT' => $this->load->view('warga/rfid', $data, true)
		));
	}

	public function process_rfid()
	{
		if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Anda belum login');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
		}

		$nik = getPost('nik');
		$rfid = getPost('rfid');

		$get = $this->warga->get(array(
			'a.nik' => $nik
		));

		if ($get->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
		}

		$row = $get->row();

		$update = array();
		$update['rfid'] = str_replace(' ', '', $rfid);

		$this->warga->update(array('nik' => $row->nik), $update);

		return JSONResponseDefault('OK', 'Berhasil');
	}

	public function process_export_warga()
	{
		if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Anda belum login');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
		}

		$start = 'A1';

		$letter = (string) preg_replace('/[0-9]+/', '', $start);
		$rowstart = (int) preg_replace('/[A-z]+/', '', $start);

		if (!$letter || !$rowstart) {
			return JSONResponseDefault('FAILED', 'Please put valid start cell of tables');
		}

		$spreadsheet = new Spreadsheet();
		$sheet = $spreadsheet->getActiveSheet();

		$letters = $letter;
		$sheet->setCellValue($letters++ . $rowstart, 'NIK')
			->setCellValue($letters++ . $rowstart, 'NOMOR KK')
			->setCellValue($letters++ . $rowstart, 'NAMA')
			->setCellValue($letters++ . $rowstart, 'ALAMAT')
			->setCellValue($letters++ . $rowstart, 'RT')
			->setCellValue($letters++ . $rowstart, 'RW')
			->setCellValue($letters++ . $rowstart, 'KODE POS')
			->setCellValue($letters++ . $rowstart, 'JENIS KELAMIN(L/P)')
			->setCellValue($letters++ . $rowstart, 'TEMPAT LAHIR')
			->setCellValue($letters++ . $rowstart, 'TANGGAL LAHIR')
			->setCellValue($letters++ . $rowstart, 'AGAMA')
			->setCellValue($letters++ . $rowstart, 'PENDIDIKAN')
			->setCellValue($letters++ . $rowstart, 'PEKERJAAN')
			->setCellValue($letters++ . $rowstart, 'STATUS KAWIN')
			->setCellValue($letters . $rowstart++, 'STATUS HUBUNGAN');

		$warga = $this->warga->result(array(
			'a.id_user' => getCurrentIdUser()
		));

		foreach ($warga as $key => $value) {
			$letters = $letter;

			$sheet->setCellValueExplicit($letters++ . $rowstart, $value->nik, PHPExcel_Cell_DataType::TYPE_STRING)
				->setCellValueExplicit($letters++ . $rowstart, $value->nomor_kk, PHPExcel_Cell_DataType::TYPE_STRING)
				->setCellValue($letters++ . $rowstart, $value->nama)
				->setCellValue($letters++ . $rowstart, $value->alamat)
				->setCellValue($letters++ . $rowstart, $value->rt)
				->setCellValue($letters++ . $rowstart, $value->rw)
				->setCellValue($letters++ . $rowstart, $value->kode_pos)
				->setCellValue($letters++ . $rowstart, $value->jenis_kelamin != 'Perempuan' ? 'L' : 'P')
				->setCellValue($letters++ . $rowstart, $value->tempat_lahir)
				->setCellValue($letters++ . $rowstart, $value->tanggal_lahir)
				->setCellValue($letters++ . $rowstart, $value->agama)
				->setCellValue($letters++ . $rowstart, $value->pendidikan)
				->setCellValue($letters++ . $rowstart, $value->pekerjaan)
				->setCellValue($letters++ . $rowstart, $value->status_perkawinan)
				->setCellValue($letters . $rowstart++, $value->status_hubungan_dalam_keluarga);
		}

		foreach (range('A', $sheet->getHighestDataColumn()) as $col) {
			$sheet->getColumnDimension($col)
				->setAutoSize(true);
		}

		$sheet->getStyle("A1:$letters" . --$rowstart)->applyFromArray(array(
			'borders' => array(
				'allBorders' => array(
					'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN
				)
			)
		));

		$writer = new Xlsx($spreadsheet);

		header('Content-Type: application/vnd.ms-excel');
		header('Content-Disposition: attachment;filename="Warga.xlsx"');
		header('Cache-Control: max-age=0');

		$writer->save('php://output');
	}

	public function api_get_detail()
	{
		$nik = getPost('nik');

		$warga = $this->warga->get(array(
			'nik' => $nik
		));

		if ($warga->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Warga tidak terdaftar pada sistem kami');
		}

		$row = $warga->row();

		return JSONResponse(array(
			'RESULT' => 'OK',
			'DATA' => $row
		));
	}

	public function importexcel_bip()
	{
		if (!isPostAjax()) {
			return JSONResponseRequestReject();
		} else if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Session required');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
		}

		return JSONResponse(array(
			'RESULT' => 'OK',
			'CONTENT' => $this->load->view('warga/importexcel_bip', array(), true)
		));
	}

	public function preview_importexcel_bip()
	{
		if (!isPostAjax()) {
			return JSONResponseRequestReject();
		} else if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Session required');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
		}

		try {
			$upload = doUpload_CloudStorage('file', 'xlsx|xls|xlsm');
		} catch (Exception $ex) {
			return JSONResponseDefault('FAILED', $ex->getMessage());
		}

		$parse = parseBIP(asset_url($upload['name']));

		if ($parse == null) {
			return JSONResponseDefault('FAILED', 'Gagal melakukan pengecekan file bip');
		}

		$outputs = array_slice($parse, 0, 10);
		$other_array_count = count($parse) - 10;

		return JSONResponse(array(
			'RESULT' => 'OK',
			'DATA' => $this->load->view('warga/preview_importexcel_bip', array(
				'outputs' => $outputs,
				'other_array_count' => $other_array_count,
				'url' => asset_url($upload['name'])
			), true)
		));
	}

	public function approve_importexcel_bip()
	{
		if (!isPostAjax()) {
			return JSONResponseRequestReject();
		} else if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Session required');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
		}

		$getdesa = $this->msusers->select('b.id_kelurahan, b.id_kecamatan, b.id_kabkota')
			->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
			->getDefaultData(array(
				'id' => getCurrentIdUser()
			));

		if ($getdesa->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Desa tidak ditemukan');
		}

		$row = $getdesa->row();

		$url = getPost('url');

		$parse = parseBIP($url);

		if (!isset($parse->error)) {
			$lakilaki = 0;
			$perempuan = 0;
			$tidaksekolah = 0;
			$belumtamat = 0;
			$tamatsd = 0;
			$sltp = 0;
			$slta = 0;
			$diploma1 = 0;
			$diploma3 = 0;
			$diploma4 = 0;
			$art = 0;
			$tidakbekerja = 0;
			$pelajar = 0;
			$wiraswasta = 0;
			$buruh = 0;
			$pns = 0;
			$swasta = 0;

			foreach ($parse as $key => $value) {
				$get = $this->warga->get(array(
					'nik' => $value->nik
				));

				if (in_array($value->jk, array('L', 'LK'))) {
					$lakilaki += 1;
				} else if (in_array($value->jk, array('P', 'PR'))) {
					$perempuan += 1;
				} else {
					$lakilaki += 1;
				}

				$pendidikan = $value->pendidikan;
				if ($pendidikan == 'Tidak/Belum Sekolah') {
					$tidaksekolah += 1;
				} else if ($pendidikan == 'Tidak Tamat SD/Sederajat') {
					$belumtamat += 1;
				} else if ($pendidikan == 'Tamat SD/Sederajat') {
					$tamatsd += 1;
				} else if ($pendidikan == 'SLTP/Sederajat') {
					$sltp += 1;
				} else if ($pendidikan == 'SLTA/Sederajat') {
					$slta += 1;
				} else if ($pendidikan == 'Diploma I/II') {
					$diploma1 += 1;
				} else if ($pendidikan == 'Diploma III/Sarjana Muda') {
					$diploma3 += 1;
				} else if ($pendidikan == 'Diploma IV/Starta 1') {
					$diploma4 += 1;
				}

				$pekerjaan = $value->pekerjaan;
				if ($pekerjaan == 'Mengurus Rumah Tangga') {
					$art += 1;
				} else if ($pekerjaan == 'Belum/Tidak Bekerja') {
					$tidakbekerja += 1;
				} else if ($pekerjaan == 'Pelajar/Mahasiswa') {
					$pelajar += 1;
				} else if ($pekerjaan == 'Wiraswasta') {
					$wiraswasta += 1;
				} else if ($pekerjaan == 'Buruh Harian Lepas') {
					$buruh += 1;
				} else if ($pekerjaan == 'Pegawai Negeri Sipil') {
					$pns += 1;
				} else if ($pekerjaan == 'Karyawan Swasta') {
					$swasta += 1;
				}

				if ($get->num_rows() == 0) {
					$insert = array();
					$insert['nik'] = $value->nik;
					$insert['nomor_kk'] = $value->kk;
					$insert['nama'] = $value->nama;
					$insert['alamat'] = $value->alamat;
					$insert['rt'] = $value->rt;
					$insert['rw'] = $value->rw;
					$insert['jenis_kelamin'] = $value->jk;
					$insert['tempat_lahir'] = $value->tmplahir;
					$insert['tanggal_lahir'] = $value->tglahir;
					$insert['agama'] = strtoupper($value->agama);
					$insert['pendidikan'] = $value->pendidikan;
					$insert['pekerjaan'] = $value->pekerjaan;
					$insert['status_perkawinan'] = $value->status;
					$insert['status_hubungan_dalam_keluarga'] = $value->hubkel;
					$insert['id_kabkota'] = $row->id_kabkota;
					$insert['id_kecamatan'] = $row->id_kecamatan;
					$insert['id_kelurahan'] = $row->id_kelurahan;
					$insert['id_user'] = getCurrentIdUser();
					$insert['createddate'] = getCurrentDate();
					$insert['createdby'] = getCurrentIdUser();
					$insert['updateddate'] = getCurrentDate();
					$insert['updatedby'] = getCurrentIdUser();
					$insert['status'] = 'Aktif';

					$this->warga->insert($insert);
				}
			}

			$get = $this->infografis->get(array('id_user' => getCurrentIdUser()));

			if ($get->num_rows() == 0) {
				$insert = array();
				$insert['laki'] = $lakilaki;
				$insert['perempuan'] = $perempuan;
				$insert['sd'] = $tamatsd;
				$insert['sltp'] = $sltp;
				$insert['belum_sekolah'] = $belumtamat;
				$insert['slta'] = $slta;
				$insert['belum_tamat'] = $belumtamat;
				$insert['dsi'] = $diploma4;
				$insert['d1'] = $diploma1;
				$insert['d2'] = $diploma1;
				$insert['d3'] = $diploma3;
				$insert['pengganguran'] = $tidakbekerja;
				$insert['ibu_rt'] = $art;
				$insert['pelajar'] = $pelajar;
				$insert['pns'] = $pns;
				$insert['karyawan_swasta'] = $swasta;
				$insert['wiraswasta'] = $wiraswasta;
				$insert['buruh_harian'] = $buruh;
				$insert['id_user'] = getCurrentIdUser();

				$this->infografis->insert($insert);
			} else {
				$row = $get->row();

				$update = array();
				$update['laki'] = $row->laki + $lakilaki;
				$update['perempuan'] = $row->perempuan + $perempuan;
				$update['sd'] = $row->sd + $tamatsd;
				$update['sltp'] = $row->sltp + $sltp;
				$update['belum_sekolah'] = $row->belum_sekolah + $belumtamat;
				$update['slta'] = $row->slta + $slta;
				$update['belum_tamat'] = $row->belum_tamat + $belumtamat;
				$update['dsi'] = $row->dsi + $diploma4;
				$update['d1'] = $row->d1 + $diploma1;
				$update['d2'] = $row->d2 + $diploma1;
				$update['d3'] = $row->d3 + $diploma3;
				$update['pengganguran'] = $row->pengganguran + $tidakbekerja;
				$update['ibu_rt'] = $row->ibu_rt + $art;
				$update['pelajar'] = $row->pelajar + $pelajar;
				$update['pns'] = $row->pns + $pns;
				$update['karyawan_swasta'] = $row->karyawan_swasta + $swasta;
				$update['wiraswasta'] = $row->wiraswasta + $wiraswasta;
				$update['buruh_harian'] = $row->buruh_harian + $buruh;

				$this->infografis->update(array('id' => $row->id), $update);
			}

			return JSONResponseDefault('OK', 'Data berhasil diimport');
		} else {
			return JSONResponseDefault('FAILED', $parse->error);
		}
	}

	public function process_delete_all()
	{
		if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Session required');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Access denied');
		}

		$this->warga->delete(array(
			'id_user' => getCurrentIdUser()
		));

		return JSONResponseDefault('OK', 'Data berhasil dihapus');
	}

	public function datatables()
	{
		if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Session required');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Access denied');
		}

		$datasource = getPost('datasource');
		$status = getPost('status');

		$datatables = $this->datatables->make('Warga_Model', 'QueryDatatables', 'SearchDatatables');

		$where = array();
		if (isAdmin()) {
			$where['a.id_user'] = getCurrentIdUser();
		}

		if ($status != null) {
			$where['a.status'] = $status;
		}

		if ($datasource == 'Sistem') {
			$where['a.datasource'] = null;
		} else if ($datasource == 'Prodeskel') {
			$where['a.datasource'] = 'Prodeskel';
		}

		$data = array();
		foreach ($datatables->getData($where) as $key => $value) {
			$detail = array();
			$detail[] = $value->nik;
			$detail[] = $value->nama;
			if ($value->tempat_lahir != null && $value->tanggal_lahir != null) {
				$detail[] = $value->tempat_lahir . ', ' . DateFormat($value->tanggal_lahir, 'd F Y');
			} else if ($value->tempat_lahir != null && $value->tanggal_lahir == null) {
				$detail[] = $value->tempat_lahir;
			} else {
				$detail[] = DateFormat($value->tanggal_lahir, 'd F Y');
			}
			$detail[] = $value->nama_kelurahan;
			$detail[] = "<button type=\"button\" class=\"btn btn-primary btn-sm mb-1\" onclick=\"scan_rfid('$value->nik')\">
				<i class=\"fa fa-id-card\"></i>
			</button>

			<button class=\"btn btn-primary btn-sm mb-1\" onclick=\"detail('$value->nik')\">
				<i class=\"fa fa-list\"></i>
			</button>

			<a href=\"" . base_url('warga/edit/' . $value->nik) . "\" class=\"btn btn-primary btn-sm mb-1\">
				<i class=\"fa fa-edit\"></i>
			</a>

			<button type=\"button\" class=\"btn btn-danger btn-sm mb-1\" onclick=\"deleteWarga('$value->nik')\">
				<i class=\"fa fa-trash\"></i>
			</button>";

			$data[] = $detail;
		}

		return $datatables->json($data);
	}

	public function importexcel_prodeskel()
	{
		if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Session required');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Access denied');
		}

		return JSONResponse(array(
			'RESULT' => 'OK',
			'CONTENT' => $this->load->view('warga/importexcel_prodeskel', array(), true)
		));
	}

	public function process_importexcel_prodeskel()
	{
		if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Session required');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Access denied');
		}

		$username = getPost('username');
		$password = getPost('password');

		if ($username == null) {
			return JSONResponseDefault('FAILED', 'Username tidak boleh kosong');
		} elseif ($password == null) {
			return JSONResponseDefault('FAILED', 'Password tidak boleh kosong');
		}

		$getdesa = $this->msusers->select('b.id_kelurahan, b.id_kecamatan, b.id_kabkota')
			->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
			->getDefaultData(array(
				'id' => getCurrentIdUser()
			));

		if ($getdesa->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'Desa tidak ditemukan');
		}

		$row = $getdesa->row();

		$get = getDataWargaProdeskel($username, $password);

		if ($get == null) {
			return JSONResponseDefault('FAILED', 'Gagal melakukan pengecekan file prodeskel');
		}

		if (isset($get->result)) {
			$data = $get->message;

			foreach ($data as $key => $value) {
				$kk = preg_replace('/[^0-9]/', '', $value->kk);
				$nik = preg_replace('/[^0-9]/', '', $value->nik);

				$get = $this->warga->get(array(
					'nik' => $nik
				));

				if ($get->num_rows() == 0) {
					$insert = array();
					$insert['nik'] = $nik;
					$insert['nomor_kk'] = $kk;
					$insert['nama'] = $value->nama;
					$insert['jenis_kelamin'] = $value->jenis_kelamin == 'LAKI-LAKI' ? 'Laki-laki' : 'Perempuan';
					$insert['tempat_lahir'] = $value->tempat_lahir;
					$insert['tanggal_lahir'] = date('Y-m-d', strtotime(str_replace('/', '-', $value->tanggal_lahir)));
					$insert['agama'] = strtoupper($value->agama);
					$insert['pendidikan'] = $value->pendidikan;
					$insert['pekerjaan'] = $value->pekerjaan;
					$insert['status_perkawinan'] = $value->status_perkawinan;
					$insert['status_hubungan_dalam_keluarga'] = $value->status_hubungan;
					$insert['id_kabkota'] = $row->id_kabkota;
					$insert['id_kecamatan'] = $row->id_kecamatan;
					$insert['id_kelurahan'] = $row->id_kelurahan;
					$insert['id_user'] = getCurrentIdUser();
					$insert['createddate'] = getCurrentDate();
					$insert['createdby'] = getCurrentIdUser();
					$insert['updateddate'] = getCurrentDate();
					$insert['updatedby'] = getCurrentIdUser();
					$insert['datasource'] = 'Prodeskel';

					$this->warga->insert($insert);
				}
			}

			return JSONResponseDefault('OK', 'Data berhasil diimport');
		} else {
			return JSONResponseDefault('FAILED', $get->message);
		}
	}

	public function datainvalid()
	{
		if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Session required');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Access denied');
		}

		return JSONResponse(array(
			'RESULT' => 'OK',
			'CONTENT' => $this->load->view('warga/datainvalid', array(
				'nik' => $this->warga->result(array(
					'id_user' => getCurrentIdUser(),
					'length(nik) !=' => 16
				)),
				'kk' => $this->warga->result(array(
					'id_user' => getCurrentIdUser(),
					'length(nomor_kk) !=' => 16
				)),
			), true)
		));
	}

	public function datainvalid_detail()
	{
		if (!isLogin()) {
			return JSONResponseDefault('FAILED', 'Session required');
		} else if (!isAdmin()) {
			return JSONResponseDefault('FAILED', 'Access denied');
		}

		$datainvalid = getPost('datainvalid');

		if ($datainvalid == "nik") {
			$groupby = "NIK";
		} else if ($datainvalid == "kk") {
			$groupby = "Kartu Keluarga";
		}

		if ($datainvalid == "nik") {
			$this->warga->where(array(
				'length(nik) !=' => 16
			));
		} else if ($datainvalid == "kk") {
			$this->warga->where(array(
				'length(nomor_kk) !=' => 16
			));
		}

		$warga = $this->warga->result(array('id_user' => getCurrentIdUser()));

		return JSONResponse(array(
			'RESULT' => 'OK',
			'CONTENT' => $this->load->view('warga/datainvalid_detail', array(
				'datainvalid' => $groupby,
				'warga' => $warga
			), true)
		));
	}
}
