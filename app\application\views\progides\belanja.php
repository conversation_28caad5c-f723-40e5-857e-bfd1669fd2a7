<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>

<!-- Hero Section -->
<section class="relative h-96 bg-cover bg-center"
    style="background-image: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');">
    <div class="absolute inset-0 flex items-center justify-center">
        <div class="text-center text-white max-w-4xl mx-auto px-4">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                Marketplace Desa
            </h1>
            <p class="text-xl md:text-2xl font-light">
                Produk dan <PERSON> <?= isset($setting->desa) ? $setting->desa : 'Desa' ?>
            </p>
        </div>
    </div>
</section>

<!-- Search Section -->
<section class="py-8 bg-gray-50">
    <div class="container mx-auto px-4">
        <form action="<?= base_url(uri_string()) ?>" method="GET" class="max-w-2xl mx-auto">
            <div class="relative">
                <input type="text"
                    name="q"
                    value="<?= isset($_GET['q']) ? $_GET['q'] : '' ?>"
                    placeholder="Cari produk, kuliner, atau jasa..."
                    class="w-full px-6 py-4 pr-12 text-lg border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                <button type="submit"
                    class="absolute right-2 top-2 bottom-2 px-6 bg-primary text-white rounded-full hover:bg-primary-dark transition-colors duration-300">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
</section>

<!-- Product Categories -->
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <!-- Category Tabs -->
        <div class="flex flex-wrap justify-center mb-12 border-b border-gray-200">
            <button onclick="showCategory('produk')"
                class="category-tab active px-6 py-3 font-medium text-gray-600 border-b-2 border-transparent hover:text-primary transition-colors duration-300"
                data-category="produk">
                Produk & Kuliner
            </button>
            <button onclick="showCategory('kerajinan')"
                class="category-tab px-6 py-3 font-medium text-gray-600 border-b-2 border-transparent hover:text-primary transition-colors duration-300"
                data-category="kerajinan">
                Kerajinan
            </button>
            <button onclick="showCategory('jasa')"
                class="category-tab px-6 py-3 font-medium text-gray-600 border-b-2 border-transparent hover:text-primary transition-colors duration-300"
                data-category="jasa">
                Jasa
            </button>
            <button onclick="showCategory('pertanian')"
                class="category-tab px-6 py-3 font-medium text-gray-600 border-b-2 border-transparent hover:text-primary transition-colors duration-300"
                data-category="pertanian">
                Pertanian
            </button>
            <button onclick="showCategory('peternakan')"
                class="category-tab px-6 py-3 font-medium text-gray-600 border-b-2 border-transparent hover:text-primary transition-colors duration-300"
                data-category="peternakan">
                Peternakan
            </button>
        </div>

        <!-- Produk & Kuliner -->
        <div id="category-produk" class="category-content">
            <div class="flex justify-between items-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800">Produk & Kuliner</h2>
                <div class="text-gray-600">
                    <?= isset($produk) ? count($produk) : 0 ?> produk ditemukan
                </div>
            </div>

            <?php if (isset($produk) && count($produk) > 0) : ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <?php foreach ($produk as $item) : ?>
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                            <div class="relative">
                                <img src="<?= asset_url($item->foto) ?>"
                                    alt="<?= $item->nama ?>"
                                    class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">

                                <!-- Category Badge -->
                                <div class="absolute top-4 left-4">
                                    <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                                        <?= $item->kategori ?>
                                    </span>
                                </div>

                                <!-- Favorite Button -->
                                <button class="absolute top-4 right-4 w-8 h-8 bg-white bg-opacity-80 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all duration-300">
                                    <i class="fas fa-heart text-gray-400 hover:text-red-500"></i>
                                </button>
                            </div>

                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">
                                    <?= $item->nama ?>
                                </h3>

                                <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                                    <?= isset($item->deskripsi) && $item->deskripsi !== null ? character_limiter($item->deskripsi, 80) : 'Tidak ada deskripsi' ?>
                                </p>

                                <!-- Contact Info -->
                                <?php if ($item->no_telp) : ?>
                                    <div class="flex items-center text-sm text-gray-500 mb-4">
                                        <i class="fas fa-phone mr-2"></i>
                                        <span><?= $item->no_telp ?></span>
                                    </div>
                                <?php endif; ?>

                                <!-- Action Buttons -->
                                <div class="flex gap-2">
                                    <a href="<?= base_url('guest/product_detail/' . $item->id) ?>"
                                        class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-lg text-sm font-medium text-center transition-colors duration-300">
                                        Detail
                                    </a>
                                    <?php if ($item->no_telp) : ?>
                                        <button onclick="pesan('<?= $item->no_telp ?>', '<?= $item->nama ?>')"
                                            class="flex-1 bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-300">
                                            Pesan
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else : ?>
                <div class="text-center py-16">
                    <div class="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-6 flex items-center justify-center">
                        <i class="fas fa-shopping-cart text-gray-400 text-3xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-4">Belum Ada Produk</h3>
                    <p class="text-gray-600 max-w-md mx-auto">
                        Saat ini belum ada produk yang tersedia dalam kategori ini.
                    </p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Kerajinan -->
        <div id="category-kerajinan" class="category-content hidden">
            <div class="flex justify-between items-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800">Kerajinan</h2>
                <div class="text-gray-600">
                    <?= isset($kerajinan) ? count($kerajinan) : 0 ?> produk ditemukan
                </div>
            </div>

            <?php if (isset($kerajinan) && count($kerajinan) > 0) : ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <?php foreach ($kerajinan as $item) : ?>
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                            <div class="relative">
                                <img src="<?= asset_url($item->foto) ?>"
                                    alt="<?= $item->nama ?>"
                                    class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">

                                <div class="absolute top-4 left-4">
                                    <span class="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                                        <?= $item->kategori ?>
                                    </span>
                                </div>
                            </div>

                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">
                                    <?= $item->nama ?>
                                </h3>

                                <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                                    <?= isset($item->deskripsi) && $item->deskripsi !== null ? character_limiter($item->deskripsi, 80) : 'Tidak ada deskripsi' ?>
                                </p>

                                <div class="flex gap-2">
                                    <a href="<?= base_url('guest/product_detail/' . $item->id) ?>"
                                        class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-lg text-sm font-medium text-center transition-colors duration-300">
                                        Detail
                                    </a>
                                    <?php if ($item->no_telp) : ?>
                                        <button onclick="pesan('<?= $item->no_telp ?>', '<?= $item->nama ?>')"
                                            class="flex-1 bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-300">
                                            Pesan
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else : ?>
                <div class="text-center py-16">
                    <div class="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-6 flex items-center justify-center">
                        <i class="fas fa-hammer text-gray-400 text-3xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-4">Belum Ada Kerajinan</h3>
                    <p class="text-gray-600 max-w-md mx-auto">
                        Saat ini belum ada produk kerajinan yang tersedia.
                    </p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Similar structure for other categories (jasa, pertanian, peternakan) -->
        <!-- For brevity, I'll add the JavaScript to handle category switching -->
    </div>
</section>

<!-- Call to Action -->
<section class="py-16 bg-primary text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">
            Ingin Menjual Produk Anda?
        </h2>
        <p class="text-xl text-white/80 mb-8 max-w-2xl mx-auto">
            Bergabunglah dengan marketplace desa kami dan jangkau lebih banyak pelanggan
        </p>
        <a href="<?= base_url('contact') ?>"
            class="inline-block bg-white text-primary px-8 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors duration-300">
            Hubungi Kami
        </a>
    </div>
</section>

<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .category-tab.active {
        color: #6e9e28;
        border-bottom-color: #6e9e28;
    }

    .category-content {
        animation: fadeIn 0.3s ease-in;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

<script>
    function showCategory(category) {
        // Hide all category contents
        document.querySelectorAll('.category-content').forEach(content => {
            content.classList.add('hidden');
        });

        // Remove active class from all tabs
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        // Show selected category content
        document.getElementById('category-' + category).classList.remove('hidden');

        // Add active class to selected tab
        document.querySelector(`[data-category="${category}"]`).classList.add('active');
    }

    function pesan(phone, productName) {
        const message = `Halo, saya tertarik dengan produk "${productName}". Bisakah Anda memberikan informasi lebih lanjut?`;
        const whatsappUrl = `https://wa.me/${phone}?text=${encodeURIComponent(message)}`;
        window.open(whatsappUrl, '_blank');
    }

    // Initialize first category
    document.addEventListener('DOMContentLoaded', function() {
        showCategory('produk');
    });
</script>