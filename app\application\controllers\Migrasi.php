<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON><PERSON><PERSON> extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Provinsi', 'provinsi');
        $this->load->model('<PERSON><PERSON><PERSON><PERSON>_Migrasi', 'permintaanmigrasi');
        $this->load->model('Warga_Model', 'warga');
        $this->load->model('Kelurahan_Model', 'kelurahan');
        $this->load->model('Master_Users', 'users');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $currentkelurahan = getCurrentIdKelurahan();
        $where = array(
            "(a.kelurahan = $currentkelurahan OR b.id_kelurahan = $currentkelurahan) =" => true
        );

        $data = array();
        $data['title'] = 'Migrasi Antar-Desa';
        $data['content'] = 'migrasi/index';
        $data['permintaan'] = $this->permintaanmigrasi->select('a.id, b.id_kelurahan AS id_kelurahan_asal, a.kelurahan AS id_kelurahan_tujuan, a.tipemigrasi, b.nama, c.nama_kelurahan AS nama_kelurahan_tujuan, a.status, d.nama_kelurahan AS nama_kelurahan_asal')
            ->join('warga b', 'b.nik = a.wargaid')
            ->join('kelurahan c', 'c.id_kelurahan = a.kelurahan')
            ->join('kelurahan d', 'd.id_kelurahan = b.id_kelurahan')
            ->result($where);

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $datatables = $this->datatables->make('Permintaan_Migrasi', 'QueryDatatables', 'SearchDatatables');

        $currentkelurahan = getCurrentIdKelurahan();
        $where = array(
            "(a.kelurahan = $currentkelurahan OR b.id_kelurahan = $currentkelurahan) =" => true
        );

        $data = array();
        foreach ($datatables->getData($where) as $key => $value) {
            if ($value->status == 'Pending') {
                $status = "<span class=\"badge badge-warning\">$value->status</span>";
            } else if ($value->status == 'Diproses') {
                $status = "<span class=\"badge badge-primary\">$value->status</span>";
            } else if ($value->status == 'Ditolak') {
                $status = "<span class=\"badge badge-danger\">$value->status</span>";
            } else if ($value->status == 'Disetujui') {
                $status = "<span class=\"badge badge-success\">$value->status</span>";
            }

            if ($value->status == 'Pending' && $value->id_kelurahan_asal == getCurrentIdKelurahan()) {
                $actions = "<button type=\"button\" class=\"btn btn-primary btn-sm\" onclick=\"requestMigrate('$value->id')\">
                    <i class=\"fa fa-paper-plane\"></i>
                    <span class=\"ml-1\">Kirim Pengajuan</span>
                </button>

                <a href=\"" . base_url('migrasi/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm\">
                    <i class=\"fa fa-edit\"></i>
                </a>

                <button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteRequest('$value->id')\">
                    <i class=\"fa fa-trash\"></i>
                </button>";
            } else if ($value->status == 'Diproses' && $value->id_kelurahan_tujuan == getCurrentIdKelurahan()) {
                $actions = "<button type=\"button\" class=\"btn btn-primary btn-sm\" onclick=\"approveMigrate('$value->id')\">
                    <i class=\"fa fa-check\"></i>
                    <span class=\"ml-1\">Terima Pengajuan</span>
                </button>

                <button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"rejectMigrate('$value->id')\">
                    <i class=\"fa fa-times\"></i>
                    <span class=\"ml-1\">Tolak Pengajuan</span>
                </button>";
            } else {
                $actions = "N/A";
            }

            $detail = array();
            $detail[] = $value->nama;
            $detail[] = $value->tipemigrasi;
            $detail[] = $value->nama_kelurahan_asal;
            $detail[] = $value->nama_kelurahan_tujuan;
            $detail[] = $status;
            $detail[] = $actions;

            $data[] = $detail;
        }

        return $datatables->json($data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Migrasi Antar-Desa';
        $data['content'] = 'migrasi/add';
        $data['status_hubkel'] = $this->db->query("SELECT status_hubungan_dalam_keluarga FROM warga GROUP BY status_hubungan_dalam_keluarga")->result();
        $data['provinsi'] = $this->provinsi->order_by('nama_provinsi', 'ASC')->result();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $nik = getPost('nik');
        $nomor_kk = getPost('nomor_kk');
        $alamat = getPost('alamat');
        $rt = getPost('rt');
        $rw = getPost('rw');
        $kodepos = getPost('kodepos');
        $status_perkawinan = getPost('status_perkawinan');
        $status_keluarga = getPost('status_keluarga');
        $provinsi = getPost('provinsi');
        $kabupaten = getPost('kabupaten');
        $kecamatan = getPost('kecamatan');
        $desa = getPost('desa');
        $samedata_statusperkawinan = getPost('samedata_statusperkawinan');
        $samedata_status_hubkel = getPost('samedata_status_hubkel');

        $validate_nik = $this->warga->get(array(
            'nik' => $nik,
            'id_user' => getCurrentIdUser()
        ));

        if ($validate_nik->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'NIK yang anda masukkan tidak ditemukan');
        }

        $row = $validate_nik->row();

        $kelurahan = $this->kelurahan->get(array(
            'id_kelurahan' => $desa
        ));

        if ($kelurahan->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Desa yang anda masukkan tidak ditemukan');
        }

        $insert = array();
        $insert['wargaid'] = $row->nik;
        $insert['nomorkk'] = $nomor_kk;
        $insert['nik'] = $row->nik;
        $insert['alamat'] = $alamat;
        $insert['rt'] = $rt;
        $insert['rw'] = $rw;
        $insert['kodepos'] = $kodepos;
        if ($samedata_statusperkawinan) {
            $insert['status_perkawinan'] = $row->status_perkawinan;
        } else {
            $insert['status_perkawinan'] = $status_perkawinan;
        }
        if ($samedata_status_hubkel) {
            $insert['status_hubkel'] = $row->status_hubungan_dalam_keluarga;
        } else {
            $insert['status_hubkel'] = $status_keluarga;
        }
        $insert['kelurahan'] = $kelurahan->row()->id_kelurahan;
        $insert['provinsiid'] = $provinsi;
        $insert['kabkotaid'] = $kabupaten;
        $insert['kecamatanid'] = $kecamatan;
        $insert['status'] = 'Pending';
        $insert['tipemigrasi'] = 'Antar Desa';
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $this->permintaanmigrasi->insert($insert);

        return JSONResponseDefault('OK', 'Permintaan migrasi berhasil dikirim');
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->permintaanmigrasi->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Permintaan migrasi tidak ditemukan');
        }

        $this->permintaanmigrasi->delete(array(
            'id' => $id
        ));

        return JSONResponseDefault('OK', 'Permintaan migrasi berhasil dihapus');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->permintaanmigrasi->select('a.*')
            ->get(array(
                'a.id' => $id
            ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('migrasi'));
        }

        $data = array();
        $data['title'] = 'Ubah Migrasi Antar-Desa';
        $data['content'] = 'migrasi/edit';
        $data['permintaan'] = $get->row();
        $data['status_hubkel'] = $this->db->query("SELECT status_hubungan_dalam_keluarga FROM warga GROUP BY status_hubungan_dalam_keluarga")->result();
        $data['provinsi'] = $this->provinsi->order_by('nama_provinsi', 'ASC')->result();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->permintaanmigrasi->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Permintaan migrasi tidak ditemukan');
        }

        $nik = getPost('nik');
        $nomor_kk = getPost('nomor_kk');
        $alamat = getPost('alamat');
        $rt = getPost('rt');
        $rw = getPost('rw');
        $kodepos = getPost('kodepos');
        $status_perkawinan = getPost('status_perkawinan');
        $status_keluarga = getPost('status_keluarga');
        $provinsi = getPost('provinsi');
        $kabupaten = getPost('kabupaten');
        $kecamatan = getPost('kecamatan');
        $desa = getPost('desa');

        $validate_nik = $this->warga->get(array(
            'nik' => $nik,
            'id_user' => getCurrentIdUser()
        ));

        if ($validate_nik->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'NIK yang anda masukkan tidak ditemukan');
        }

        $row = $validate_nik->row();

        $kelurahan = $this->kelurahan->get(array(
            'id_kelurahan' => $desa
        ));

        if ($kelurahan->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Desa yang anda masukkan tidak ditemukan');
        }

        $update = array();
        $update['wargaid'] = $row->nik;
        $update['nomorkk'] = $nomor_kk;
        $update['nik'] = $row->nik;
        $update['alamat'] = $alamat;
        $update['rt'] = $rt;
        $update['rw'] = $rw;
        $update['kodepos'] = $kodepos;
        $update['status_perkawinan'] = $status_perkawinan;
        $update['status_hubkel'] = $status_keluarga;
        $update['kelurahan'] = $kelurahan->row()->id_kelurahan;
        $update['provinsiid'] = $provinsi;
        $update['kabkotaid'] = $kabupaten;
        $update['kecamatanid'] = $kecamatan;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->permintaanmigrasi->update(array(
            'id' => $id
        ), $update);

        return JSONResponseDefault('OK', 'Permintaan migrasi berhasil diubah');
    }

    public function request()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->permintaanmigrasi->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Permintaan migrasi tidak ditemukan');
        }

        $row = $get->row();

        $get_desa = $this->users->get(array(
            'kelurahanid' => $row->kelurahan
        ));

        if ($get_desa->num_rows() == 0) {
        }

        $update = array();
        $update['status'] = 'Diproses';
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->permintaanmigrasi->update(array(
            'id' => $id
        ), $update);

        return JSONResponseDefault('OK', 'Permintaan migrasi berhasil diproses');
    }

    public function approve()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->permintaanmigrasi->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Permintaan migrasi tidak ditemukan');
        }

        $row = $get->row();

        $user = $this->users->get(array(
            'kelurahanid' => $row->kelurahan
        ));

        if ($user->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Desa yang anda masukkan tidak ditemukan');
        }

        $rowUser = $user->row();

        $update = array();
        $update['nik'] = $row->nik;
        $update['nomor_kk'] = $row->nomorkk;
        $update['alamat'] = $row->alamat;
        $update['rt'] = $row->rt;
        $update['rw'] = $row->rw;
        $update['kode_pos'] = $row->kodepos;
        $update['status_perkawinan'] = $row->status_perkawinan;
        $update['status_hubungan_dalam_keluarga'] = $row->status_hubkel;
        $update['id_kelurahan'] = $row->kelurahan;
        $update['id_kecamatan'] = $row->kecamatanid;
        $update['id_kabkota'] = $row->kabkotaid;
        $update['id_user'] = $rowUser->id;

        $this->warga->update(array(
            'nik' => $row->wargaid
        ), $update);

        $update = array();
        $update['status'] = 'Disetujui';
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->permintaanmigrasi->update(array(
            'id' => $id
        ), $update);

        return JSONResponseDefault('OK', 'Permintaan migrasi berhasil disetujui');
    }

    public function reject()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->permintaanmigrasi->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Permintaan migrasi tidak ditemukan');
        }

        $update = array();
        $update['status'] = 'Ditolak';
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->permintaanmigrasi->update(array(
            'id' => $id
        ), $update);

        return JSONResponseDefault('OK', 'Permintaan migrasi berhasil ditolak');
    }
}
