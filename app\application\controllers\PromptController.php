<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Config_Prompt $config_prompt
 */
class PromptController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Config_Prompt', 'config_prompt');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Setting Prompt';
        $data['content'] = 'prompt/index';
        $data['prompt'] = $this->config_prompt->get()->row();

        return $this->load->view('master', $data);
    }

    public function process_update()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk mengakses halaman ini');
        }

        $title_news = getPost('title_news');
        $description_news = getPost('description_news');
        $description_covid_news = getPost('description_covid_news');

        if ($title_news == null) {
            return JSONResponseDefault('FAILED', 'Judul Berita tidak boleh kosong');
        } else if ($description_news == null) {
            return JSONResponseDefault('FAILED', 'Deskripsi Berita tidak boleh kosong');
        } else if ($description_covid_news == null) {
            return JSONResponseDefault('FAILED', 'Deskripsi Berita COVID19 tidak boleh kosong');
        }

        if ($this->config_prompt->total() == 0) {
            $this->config_prompt->insert([
                'title_news' => $title_news,
                'description_news' => $description_news,
                'description_covid_news' => $description_covid_news
            ]);
        } else {
            $row = $this->config_prompt->get()->row();

            $this->config_prompt->update([
                'id' => $row->id,
            ], [
                'title_news' => $title_news,
                'description_news' => $description_news,
                'description_covid_news' => $description_covid_news
            ]);
        }

        return JSONResponseDefault('OK', 'Berhasil mengubah setting prompt');
    }
}
