<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>

<!-- Hero Section -->
<section class="relative h-96 bg-cover bg-center"
    style="background-image: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1504711434969-e33886168f5c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80');">
    <div class="absolute inset-0 flex items-center justify-center">
        <div class="text-center text-white max-w-4xl mx-auto px-4">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                Detail Berita
            </h1>
            <p class="text-xl md:text-2xl font-light">
                Informasi Terkini <?= isset($setting->desa) ? $setting->desa : 'Desa' ?>
            </p>
        </div>
    </div>
</section>

<!-- Breadcrumb -->
<section class="py-8 bg-gray-100">
    <div class="container mx-auto px-4">
        <nav class="flex items-center space-x-2 text-sm">
            <a href="<?= base_url() ?>" class="text-primary hover:text-primary-dark">Beranda</a>
            <i class="fas fa-chevron-right text-gray-400"></i>
            <a href="<?= base_url('berita/all') ?>" class="text-primary hover:text-primary-dark">Berita</a>
            <i class="fas fa-chevron-right text-gray-400"></i>
            <span class="text-gray-600">Detail Berita</span>
        </nav>
    </div>
</section>

<!-- Article Content -->
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <!-- Article Header -->
            <header class="mb-8">
                <!-- Category and Date -->
                <div class="flex items-center justify-between mb-4">
                    <span class="bg-primary text-white px-4 py-2 rounded-full text-sm font-medium">
                        <?= isset($berita->kategori_berita) ? $berita->kategori_berita : 'Berita' ?>
                    </span>
                    <time class="text-gray-500 text-sm">
                        <?= date('d F Y', strtotime($berita->createddate)) ?>
                    </time>
                </div>

                <!-- Title -->
                <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 leading-tight mb-6">
                    <?= $berita->judul ?>
                </h1>

                <!-- Meta Info -->
                <div class="flex items-center space-x-6 text-sm text-gray-500 mb-8">
                    <div class="flex items-center">
                        <i class="fas fa-user mr-2"></i>
                        <span>Admin</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-eye mr-2"></i>
                        <span><?= isset($berita->views) ? number_format($berita->views) : '0' ?> views</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-calendar mr-2"></i>
                        <span><?= date('d M Y, H:i', strtotime($berita->createddate)) ?></span>
                    </div>
                </div>

                <!-- Featured Image -->
                <?php if ($berita->foto) : ?>
                    <div class="mb-8">
                        <img src="<?= asset_url($berita->foto) ?>"
                            alt="<?= $berita->judul ?>"
                            class="w-full h-96 object-cover rounded-2xl shadow-lg">
                    </div>
                <?php endif; ?>
            </header>

            <!-- Article Body -->
            <article class="prose prose-lg max-w-none">
                <div class="text-gray-700 leading-relaxed">
                    <?= isset($berita->deskripsi) && $berita->deskripsi !== null ? $berita->deskripsi : 'Konten tidak tersedia' ?>
                </div>
            </article>

            <!-- Share Buttons -->
            <div class="mt-12 pt-8 border-t border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Bagikan Artikel</h3>
                <div class="flex items-center space-x-4">
                    <!-- Facebook -->
                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?= current_url() ?>"
                        target="_blank"
                        class="flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors duration-300">
                        <i class="fab fa-facebook-f"></i>
                    </a>

                    <!-- Twitter -->
                    <a href="https://twitter.com/intent/tweet?url=<?= current_url() ?>&text=<?= urlencode($berita->judul) ?>"
                        target="_blank"
                        class="flex items-center justify-center w-12 h-12 bg-blue-400 text-white rounded-full hover:bg-blue-500 transition-colors duration-300">
                        <i class="fab fa-twitter"></i>
                    </a>

                    <!-- WhatsApp -->
                    <a href="https://wa.me/?text=<?= urlencode($berita->judul . ' - ' . current_url()) ?>"
                        target="_blank"
                        class="flex items-center justify-center w-12 h-12 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors duration-300">
                        <i class="fab fa-whatsapp"></i>
                    </a>

                    <!-- Copy Link -->
                    <button onclick="copyToClipboard('<?= current_url() ?>')"
                        class="flex items-center justify-center w-12 h-12 bg-gray-500 text-white rounded-full hover:bg-gray-600 transition-colors duration-300">
                        <i class="fas fa-link"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related News -->
<?php if (isset($berita_terkait) && count($berita_terkait) > 0) : ?>
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                <h2 class="text-3xl font-bold text-gray-800 mb-12 text-center">Berita Terkait</h2>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <?php foreach (array_slice($berita_terkait, 0, 3) as $related) : ?>
                        <article class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                            <div class="relative">
                                <img src="<?= asset_url($related->foto) ?>"
                                    alt="<?= $related->judul ?>"
                                    class="w-full h-48 object-cover">

                                <div class="absolute top-4 left-4">
                                    <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                                        <?= isset($related->kategori_berita) ? $related->kategori_berita : 'Berita' ?>
                                    </span>
                                </div>
                            </div>

                            <div class="p-6">
                                <h3 class="text-lg font-bold text-gray-800 mb-3 line-clamp-2">
                                    <?= $related->judul ?>
                                </h3>

                                <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                                    <?= isset($related->deskripsi) && $related->deskripsi !== null ? character_limiter(strip_tags($related->deskripsi), 80) : 'Tidak ada konten' ?>
                                </p>

                                <div class="flex items-center justify-between text-xs text-gray-500 mb-4">
                                    <span><?= date('d M Y', strtotime($related->createddate)) ?></span>
                                    <span><?= isset($related->views) ? number_format($related->views) : '0' ?> views</span>
                                </div>

                                <a href="<?= base_url('berita/' . date('Y', strtotime($related->createddate)) . '/' . $related->link_access . '?id=' . $related->id) ?>"
                                    class="inline-flex items-center text-primary hover:text-primary-dark font-medium text-sm">
                                    Baca Selengkapnya
                                    <i class="fas fa-arrow-right ml-2"></i>
                                </a>
                            </div>
                        </article>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </section>
<?php endif; ?>

<!-- Back to News -->
<section class="py-8 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center">
            <a href="<?= base_url('berita/all') ?>"
                class="inline-flex items-center bg-primary hover:bg-primary-dark text-white px-8 py-3 rounded-full font-medium transition-colors duration-300">
                <i class="fas fa-arrow-left mr-2"></i>
                Kembali ke Berita
            </a>
        </div>
    </div>
</section>

<style>
    /* Prose styles for article content */
    .prose {
        color: #374151;
        max-width: none;
    }

    .prose h1,
    .prose h2,
    .prose h3,
    .prose h4,
    .prose h5,
    .prose h6 {
        color: #1f2937;
        font-weight: 700;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }

    .prose h1 {
        font-size: 2.25rem;
    }

    .prose h2 {
        font-size: 1.875rem;
    }

    .prose h3 {
        font-size: 1.5rem;
    }

    .prose h4 {
        font-size: 1.25rem;
    }

    .prose p {
        margin-bottom: 1.5rem;
        line-height: 1.75;
    }

    .prose ul,
    .prose ol {
        margin-bottom: 1.5rem;
        padding-left: 1.5rem;
    }

    .prose li {
        margin-bottom: 0.5rem;
    }

    .prose blockquote {
        border-left: 4px solid #6e9e28;
        padding-left: 1rem;
        margin: 2rem 0;
        font-style: italic;
        color: #6b7280;
    }

    .prose img {
        border-radius: 0.5rem;
        margin: 2rem 0;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    .prose a {
        color: #6e9e28;
        text-decoration: underline;
    }

    .prose a:hover {
        color: #5a7f20;
    }

    /* Line clamp utility */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>

<script>
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            // Show success message
            const button = event.target.closest('button');
            const originalIcon = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i>';
            button.classList.remove('bg-gray-500', 'hover:bg-gray-600');
            button.classList.add('bg-green-500', 'hover:bg-green-600');

            setTimeout(() => {
                button.innerHTML = originalIcon;
                button.classList.remove('bg-green-500', 'hover:bg-green-600');
                button.classList.add('bg-gray-500', 'hover:bg-gray-600');
            }, 2000);
        }).catch(function(err) {
            console.error('Could not copy text: ', err);
        });
    }
</script>