<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Kategori_InfografisModel $kategori_info
 * @property Datatables $datatables
 */
class Kategori_InfoGrafis extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Kategori_InfografisModel', 'kategori_info');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = 'Kategori Infografis';
        $data['content'] = 'kategori_infografis/index';
        $data['kategori'] = $this->kategori_info->getDefaultData(array(
            'a.id_user' => getCurrentIdUser()
        ))->result();

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $datatables = $this->datatables->make('Kategori_InfografisModel', 'QueryDatatables', 'SearchDatatables');

        $data = array();
        foreach ($datatables->getData(array('a.id_user' => getCurrentIdUser())) as $key => $value) {
            if ($value->icon != null) {
                $foto = "<img src=\"" . asset_url($value->icon) . "\" width=\"100\">";
            } else {
                $foto = "- Foto Tidak Ditemukan -";
            }

            $actions = "";
            if ($value->is_primary == null || get_subdomain() == null) {
                $actions = "<a href=\"" . base_url('kategori_infografis/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm\">
                    <i class=\"fa fa-edit\"></i>
                </a>

                <button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteKategori($value->id)\">
                    <i class=\"fa fa-trash\"></i>
                </button>";
            } else {
                $actions = "N/A";
            }

            $detail = array();
            $detail[] = $value->nama;
            $detail[] = $foto;
            $detail[] = $value->type;
            $detail[] = $actions;

            $data[] = $detail;
        }

        return $datatables->json($data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = 'Kategori Infografis - Add';
        $data['content'] = 'kategori_infografis/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $nama = getPost('nama');
        $type = getPost('type');

        try {
            $upload = doUpload_CloudStorage('icon', 'jpg|png|jpeg');
            $file_name = $upload['name'];

            $insert = array();
            $insert['id_user'] = getCurrentIdUser();
            $insert['nama'] = $nama;
            $insert['icon'] = $file_name;
            $insert['type'] = $type;

            $this->kategori_info->insert($insert);

            return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->kategori_info->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $delete = $this->kategori_info->delete(array(
            'id' => $id,
        ));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus data');
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return redirect('dashboard');
        }

        $get = $this->kategori_info->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect('kategori_infografis');
        }

        $data = array();
        $data['title'] = 'Kategori Infografis - Edit';
        $data['content'] = 'kategori_infografis/edit';
        $data['kategori'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->kategori_info->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $nama = getPost('nama');
        $type = getPost('type');

        $update = array();
        if ($_FILES['icon']['size'] > 0) {
            try {
                $upload = doUpload_CloudStorage('icon', 'jpg|png|jpeg');
                $update['icon'] = $upload['name'];
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        }

        $update['nama'] = $nama;
        $update['type'] = $type;

        $this->kategori_info->update(array('id' => $id), $update);

        return JSONResponseDefault('OK', 'Data berhasil diubah');
    }
}
