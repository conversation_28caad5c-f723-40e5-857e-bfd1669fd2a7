<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Berita_Model $berita
 * @property Notifikasi_Berita $notifikasiberita
 * @property Datatables $datatables
 * @property Master_Users $masterusers
 */
class News extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Berita_Model', 'berita');
        $this->load->model('Kategori_Berita', 'kategoriberita');
        $this->load->model('Notifikasi_Berita', 'notifikasiberita');
        $this->load->model('Master_Users', 'masterusers');
    }

    public function index()
    {
        $status = getGet('status');
        $kabupaten = getGet('kabupaten');
        $kecamatan = getGet('kecamatan');

        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = 'Berita';
        $data['content'] = 'berita/index';
        $data['status'] = $status;

        $kabupatenResult = $this->masterusers->select('d.id_kabkota, d.nama_kabkota')
            ->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
            ->join('kecamatan c', 'c.id_kecamatan = b.id_kecamatan')
            ->join('kabkota d', 'd.id_kabkota = c.id_kabkota')
            ->order_by('d.nama_kabkota')
            ->group_by('d.id_kabkota, d.nama_kabkota')
            ->result();

        $data['kabupatendesa'] = $kabupatenResult;
        $data['selectedkabupaten'] = $kabupaten;
        $data['selectedkecamatan'] = $kecamatan;

        return $this->load->view('master', $data);
    }

    public function verify()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk melakukan aksi ini');
        }

        $id = getPost('id');

        $get = $this->berita->get(array(
            'id' => $id,
            'isverified' => null
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Berita tidak ditemukan');
        }

        $data = array(
            'isverified' => 1,
            'updateddate' => getCurrentDate(),
            'updatedby' => getCurrentIdUser()
        );

        $this->berita->update(array(
            'id' => $id
        ), $data);

        return JSONResponseDefault('OK', 'Berita berhasil diverifikasi');
    }

    public function reject()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk melakukan aksi ini');
        }

        $id = getPost('id');

        $get = $this->berita->get(array(
            'id' => $id,
            'isverified' => null
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Berita tidak ditemukan');
        }

        $data = array(
            'isverified' => 2,
            'updateddate' => getCurrentDate(),
            'updatedby' => getCurrentIdUser()
        );

        $this->berita->update(array(
            'id' => $id
        ), $data);

        return JSONResponseDefault('OK', 'Berita berhasil ditolak');
    }

    public function notification()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Notifikasi Berita';
        $data['content'] = 'berita/notification/index';
        $data['notification'] = $this->notifikasiberita->result();

        return $this->load->view('master', $data);
    }

    public function add_notification()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Notifikasi Berita';
        $data['content'] = 'berita/notification/add';

        return $this->load->view('master', $data);
    }

    public function process_add_notification()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk melakukan aksi ini');
        }

        $name = getPost('name');
        $phonenumber = getPost('phonenumber');

        $get = $this->notifikasiberita->get(array(
            'phonenumber' => $phonenumber
        ));

        if ($get->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Nomor telepon sudah terdaftar');
        }

        $data = array(
            'name' => $name,
            'phonenumber' => $phonenumber
        );

        $this->notifikasiberita->insert($data);

        return JSONResponseDefault('OK', 'Notifikasi berhasil ditambahkan');
    }

    public function edit_notification($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->notifikasiberita->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/news/notification'));
        }

        $data = array();
        $data['title'] = 'Edit Notifikasi Berita';
        $data['content'] = 'berita/notification/edit';
        $data['notification'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit_notification($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk melakukan aksi ini');
        }

        $name = getPost('name');
        $phonenumber = getPost('phonenumber');

        $get = $this->notifikasiberita->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Notifikasi tidak ditemukan');
        }

        $data = array(
            'name' => $name,
            'phonenumber' => $phonenumber
        );

        $this->notifikasiberita->update(array(
            'id' => $id
        ), $data);

        return JSONResponseDefault('OK', 'Notifikasi berhasil diubah');
    }

    public function process_delete_notification()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk melakukan aksi ini');
        }

        $id = getPost('id');

        $get = $this->notifikasiberita->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Notifikasi tidak ditemukan');
        }

        $this->notifikasiberita->delete(array(
            'id' => $id
        ));

        return JSONResponseDefault('OK', 'Notifikasi berhasil dihapus');
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isAdmin() && !isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk melakukan aksi ini');
        }

        $datatables = $this->datatables->make('Berita_Model', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        if (isAdmin()) {
            $where['a.id_user'] = getCurrentIdUser();
        }

        $status = getPost('status');
        $kabupaten = getPost('kabupaten');
        $kecamatan = getPost('kecamatan');

        if ($status == 1) {
            $where['a.isverified'] = 1;
        } else if ($status == 2) {
            $where['a.isverified'] = 2;
        } else {
            $where['a.isverified'] = null;
        }

        if ($kabupaten != null) {
            $where['e.id_kabkota'] = $kabupaten;
        }

        if ($kecamatan != null) {
            $where['e.id_kecamatan'] = $kecamatan;
        }

        $data = array();
        foreach ($datatables->getData($where) as $key => $value) {
            if ($value->isverified == null) {
                $isverified = "<label class=\"badge badge-warning\">Menunggu Konfirmasi Admin</label>";
            } elseif ($value->isverified == 1) {
                $isverified = "<label class=\"badge badge-success\">Diterbitkan</label>";
            } elseif ($value->isverified == 2) {
                $isverified = "<label class=\"badge badge-danger\">Ditolak</label>";
            }

            if ($value->foto != null) {
                $foto = "<a href=\"" . asset_url($value->foto) . "\">Preview</a>";
            } else {
                $foto = "-";
            }

            $actions = "";
            if ($value->isverified == null && (!isAdmin() && !isPMD())) {
                $actions .= "<button type=\"button\" class=\"btn btn-success btn-sm mb-1\" onclick=\"verifyBerita($value->id)\">
                    <i class=\"fa fa-check\"></i>
                </button>
                
                <button type=\"button\" class=\"btn btn-danger btn-sm mb-1\" onclick=\"rejectBerita($value->id)\">
                    <i class=\"fa fa-times\"></i>
                </button>";
            }

            $actions .= "<a href=\"" . base_url('berita/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm mb-1 mr-1\">
                <i class=\"fa fa-edit\"></i>
            </a>";

            $detail = array();
            if (!isAdmin() && !isPMD()) {
                $detail[] = $value->username;
            }
            $detail[] = DateFormat($value->tanggal, 'd F Y H:i:s');
            $detail[] = $isverified;
            $detail[] = IDR($value->total) . ' Pengunjung';
            $detail[] = IDR($value->sharescount) . ' Share';
            $detail[] = $value->judul;
            $detail[] = $value->nama_kategori ?? 'Kabar Berita';
            $detail[] = $foto;
            $detail[] = $actions;

            $data[] = $detail;
        }

        return $datatables->json($data);
    }

    public function api_villages_in()
    {
        $desa = getPost('desa', []);
        $limit = getPost('limit', 9);
        $offset = getPost('offset', 0);

        if (is_array($desa) && count($desa) > 0) {
            $this->berita->where_in('b.id_kelurahan', $desa);
        } else {
            $this->berita->where_in('b.id_kelurahan', $desa);
        }

        $news = $this->berita->select('a.*')
            ->join('msusers b', 'b.id = a.id_user')
            ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid')
            ->join('kategori_berita d', 'd.id = a.categoryid', 'LEFT')
            ->order_by('a.createddate', 'DESC')
            ->limit($limit, $offset)
            ->result();

        return JSONResponse(array(
            'success' => true,
            'data' => $news
        ));
    }
}
