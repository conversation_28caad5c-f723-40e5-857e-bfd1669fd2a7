<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="content buku-profil-desa">
    <div class="bg-blur-left"></div>
    <div class="bg-blur-right"></div>

    <div class="container position-relative">
        <div class="text-center mb-5">
            <h3>Buku Profil Desa</h3>
            <p class="subtitle">Dokumen resmi yang berisi informasi lengkap tentang profil <?= getValueObj($setting, 'desa') ? 'Desa ' . getValueObj($setting, 'desa') : 'desa' ?></p>

            <div class="mt-4">
                <a href="<?= base_url('buku-profil-desa/download') ?>" class="btn btn-success" target="_blank">
                    <i class="fa fa-download mr-2"></i>
                    Download PDF
                </a>
            </div>
        </div>

        <div class="card shadow-lg">
            <div class="card-body p-0">
                <div class="pdf-viewer-container" style="height: 80vh; min-height: 600px;">
                    <iframe
                        src="<?= $pdf_url ?>"
                        width="100%"
                        height="100%"
                        style="border: none; border-radius: 8px;"
                        title="Buku Profil Desa">
                    </iframe>
                </div>

                <!-- Fallback for browsers that don't support iframe PDF viewing -->
                <div class="text-center p-5" id="pdf-fallback" style="display: none;">
                    <i class="fa fa-file-pdf-o fa-4x text-primary mb-4"></i>
                    <h4>Tidak dapat menampilkan PDF</h4>
                    <p class="text-muted mb-4">Browser Anda tidak mendukung tampilan PDF secara langsung.</p>
                    <a href="<?= $pdf_url ?>" class="btn btn-primary btn-lg" target="_blank">
                        <i class="fa fa-external-link mr-2"></i>
                        Buka PDF di Tab Baru
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if iframe can load PDF
        const iframe = document.querySelector('iframe');
        const fallback = document.getElementById('pdf-fallback');

        iframe.onload = function() {
            try {
                // Try to access iframe content to check if PDF loaded
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                if (!iframeDoc || iframeDoc.body.innerHTML.includes('error')) {
                    throw new Error('PDF load failed');
                }
            } catch (e) {
                // If we can't access iframe content or there's an error, show fallback
                iframe.style.display = 'none';
                fallback.style.display = 'block';
            }
        };

        iframe.onerror = function() {
            iframe.style.display = 'none';
            fallback.style.display = 'block';
        };
    });
</script>