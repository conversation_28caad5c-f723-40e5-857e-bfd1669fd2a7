<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>

<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="hero-title">Bantuan Sosial</h1>
                <p class="hero-subtitle">Informasi Program Bantuan Sosial untuk Masyarakat <?= isset($setting->desa) ? $setting->desa : '' ?></p>
            </div>
            <div class="col-lg-6">
                <div class="hero-image">
                    <i class="fas fa-hands-helping fa-10x text-white"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="content-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-header text-center mb-5">
                    <h2>Program Bantuan Sosial</h2>
                    <p class="text-muted">Daftar program bantuan sosial yang telah disalurkan kepada masyarakat</p>
                </div>
            </div>
        </div>

        <?php if (!empty($penerimaan_blt)): ?>
            <div class="row">
                <?php foreach ($penerimaan_blt as $key => $value): ?>
                    <div class="col-lg-6 col-md-6 mb-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h5 class="card-title text-primary"><?= $value->deskripsi ?></h5>
                                    <span class="badge badge-success">Aktif</span>
                                </div>

                                <div class="mb-3">
                                    <h6 class="text-success mb-2">
                                        <i class="fas fa-money-bill-wave"></i>
                                        Rp <?= number_format($value->nominal, 0, ',', '.') ?>
                                    </h6>
                                </div>

                                <div class="row text-center mb-3">
                                    <div class="col-4">
                                        <div class="stat-item">
                                            <h6 class="text-primary mb-1"><?= $value->total_penerima ?></h6>
                                            <small class="text-muted">Total Penerima</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-item">
                                            <h6 class="text-success mb-1"><?= $value->terverifikasi ?></h6>
                                            <small class="text-muted">Terverifikasi</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-item">
                                            <h6 class="text-warning mb-1"><?= $value->menunggu_verifikasi ?></h6>
                                            <small class="text-muted">Menunggu</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i>
                                        <?= date('d M Y', strtotime($value->tanggal_dibuat)) ?>
                                    </small>
                                    <a href="<?= base_url('bantuan-sosial/detail/' . $value->id) ?>" class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye"></i>
                                        Lihat Detail
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="row">
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-info-circle fa-5x text-muted mb-3"></i>
                        <h4 class="text-muted">Belum Ada Program Bantuan Sosial</h4>
                        <p class="text-muted">Saat ini belum ada program bantuan sosial yang tersedia.</p>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Info Section -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card bg-light">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-lg-8">
                                <h5 class="mb-2 text-white">
                                    <i class="fas fa-info-circle text-primary"></i>
                                    Informasi Penting
                                </h5>
                                <p class="mb-0 text-white">
                                    Data yang ditampilkan adalah penerima bantuan sosial yang telah terverifikasi.
                                    Untuk informasi lebih lanjut, silakan hubungi kantor desa.
                                </p>
                            </div>
                            <div class="col-lg-4 text-lg-right">
                                <a href="<?= base_url('guestbook') ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-phone"></i>
                                    Hubungi Kami
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .hero-section {
        background: linear-gradient(135deg, #0A142F 0%, #1a2a4a 100%);
        color: white;
        padding: 100px 0 80px;
    }

    .hero-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
    }

    .hero-image {
        text-align: center;
    }

    .content-section {
        padding: 80px 0;
    }

    .section-header h2 {
        font-size: 2.5rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1rem;
    }

    .card {
        border: none;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
    }

    .stat-item h6 {
        font-size: 1.5rem;
        font-weight: 600;
    }

    .badge {
        font-size: 0.75rem;
    }

    @media (max-width: 768px) {
        .hero-title {
            font-size: 2rem;
        }

        .hero-subtitle {
            font-size: 1rem;
        }

        .section-header h2 {
            font-size: 2rem;
        }
    }
</style>