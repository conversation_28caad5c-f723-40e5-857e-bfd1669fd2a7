<?php

use Spipu\Html2Pdf\Html2Pdf;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Pemerintahan_Model $pemerintahan
 * @property CI_Upload $upload
 * @property Setting_Umum $settingumum
 * @property Kontak_Penting $kontakpenting
 * @property Struktur_Organisasi $strukturorganisasi
 */
class Pemerintahan extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Pemerintahan_Model', 'pemerintahan');
        $this->load->model('Kontak_Penting', 'kontakpenting');
        $this->load->model('Setting_Umum', 'settingumum');
        $this->load->model('Master_Users', 'masterusers');
        $this->load->model('Struktur_Organisasi', 'strukturorganisasi');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        }

        $where = array();
        if (getSessionValue('ROLE') == 'admin') {
            $where['a.id_user'] = getCurrentIdUser();
        }

        $pemerintahan = $this->pemerintahan->getDefaultData($where);

        $data = array();
        $data['title'] = 'Pemerintahan';
        $data['content'] = 'pemerintahan';
        if ($pemerintahan->num_rows() > 0) {
            $data['pemerintahan'] = $pemerintahan->row();
        } else {
            $data['pemerintahan'] = null;
        }

        return $this->load->view('master', $data);
    }

    public function process_update()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        }

        $visi = getPost('visi');
        $misi = getPost('misi');

        $pemerintahan = $this->pemerintahan->getDefaultData(array('id_user' => getCurrentIdUser()));
        $row = $pemerintahan->row();

        $exec = array();
        if (isset($_FILES['struktur']) && $_FILES['struktur']['size'] > 0) {
            try {
                $upload = doUpload_CloudStorage('struktur', 'jpg|jpeg|png');
                $exec['struktur'] = $upload['name'];
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        }

        $exec['visi'] = $visi;
        $exec['misi'] = $misi;
        $exec['id_user'] = getCurrentIdUser();

        if ($pemerintahan->num_rows() == 0) {
            $doExecute = $this->pemerintahan->insert($exec);
        } else {
            $doExecute = $this->pemerintahan->update(array('id_user' => getCurrentIdUser()), $exec);
        }

        if ($doExecute) {
            return JSONResponseDefault('OK', 'Berhasil menyimpan pemerintahan');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menyimpan pemerintahan');
        }
    }

    public function pemerintah()
    {
        $data = array();

        $where = array(
            'a.id_user' => $this->subdomain_account->id
        );

        $setting = $this->settingumum->getDefaultData($where);
        $data['title'] = 'Pemerintahan';
        $data['setting'] = $setting->row();
        $data['pemerintah'] = $this->pemerintahan->getDefaultData($where)->row();
        $data['kontakpenting'] = $this->kontakpenting->getDefaultData($where)->result();

        if ($this->subdomain_account->sync_siades == 1) {
            $data['strukturorganisasi'] = $this->strukturorganisasi->select('a.*, b.parent, b.name AS positionname, b.id AS structid')
                ->join('floworganizationchart b', 'b.id = a.jabatanid', 'LEFT')
                ->order_by('b.ordering', 'ASC')
                ->result(array(
                    'userid' => $this->subdomain_account->id,
                    'is_siades' => 1
                ));
        } else {
            $data['strukturorganisasi'] = $this->strukturorganisasi->select('a.*, b.parent, b.name AS positionname, b.id AS structid')
                ->join('floworganizationchart b', 'b.id = a.jabatanid', 'LEFT')
                ->order_by('b.ordering', 'ASC')
                ->result(array(
                    'userid' => $this->subdomain_account->id,
                    "(is_siades IS NULL OR is_siades = 0) =" => true
                ));
        }
        $data['user'] = $this->subdomain_account;

        if ($setting->num_rows() > 0) {
            // ProGides platform always uses ProGides theme regardless of theme setting
            if (getPlatformName() == 'ProGides') {
                $data['content'] = 'progides/pemerintahan';
                return $this->load->view('progides/master', $data);
            } elseif ($this->subdomain_account->themeid == 2) {
                $data['content'] = 'profile/pemerintah';
                return $this->load->view('profile/master', $data);
            } else {
                $data['content'] = 'landing/pemerintahan';
                return $this->load->view('landing/master', $data);
            }
        } else {
            return $this->load->view('profile_v2/configuration', $data);
        }
    }

    public function detail_strukturorganisasi()
    {
        $id = getPost('id');

        $get = $this->strukturorganisasi->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        $data = array();
        $data['strukturorganisasi'] = $row;

        if ($this->subdomain_account->themeid == 2) {
            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('profile/strukturorganisasi', $data, true)
            ));
        } else {
            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('landing/strukturorganisasi', $data, true)
            ));
        }
    }
}
