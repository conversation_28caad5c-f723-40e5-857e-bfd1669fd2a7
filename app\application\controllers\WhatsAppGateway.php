<?php
defined('BASEPATH') or exit('No direct script access allowed');

class WhatsAppGateway extends MY_Controller
{
    public function index()
    {
        $gideswhatsappgateway = new BaileysHelper();
        $find = $gideswhatsappgateway->session_status(stringEncryption('encrypt', getCurrentIdUser()));
        $find = json_decode($find);

        $data = array();
        $data['title'] = 'WhatsApp Gateway';
        $data['content'] = 'whatsapp_gateway/index';
        $data['find'] = $find;

        return $this->load->view('master', $data);
    }

    public function qr()
    {
        $gideswhatsappgateway = new BaileysHelper();
        $create = $gideswhatsappgateway->create_session(stringEncryption('encrypt', getCurrentIdUser()));
        $create = json_decode($create);

        if (isset($create->success)) {
            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => '<div class="text-center"><img src="' . $create->data->qr . '" alt="QR Code" /></div>'
            ));
        } else {
            return JSONResponseDefault('FAILED', 'Failed to create session');
        }
    }

    public function status()
    {
        $gideswhatsappgateway = new BaileysHelper();
        $status = $gideswhatsappgateway->session_status(stringEncryption('encrypt', getCurrentIdUser()));
        $status = json_decode($status);

        if (isset($status->success) && $status->success) {
            if (isset($status->data->status)) {
                return JSONResponse(array(
                    'RESULT' => 'OK',
                    'STATUS' => $status->data->status
                ));
            } else {
                return JSONResponseDefault('FAILED', 'Failed to get session status');
            }
        } else {
            return JSONResponseDefault('FAILED', 'Failed to get session status');
        }
    }
}
