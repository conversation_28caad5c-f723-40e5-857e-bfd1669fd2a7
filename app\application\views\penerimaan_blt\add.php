<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h4 class="card-title">Tambah Penerimaan BLT</h4>
                    <div class="card-tools">
                        <a href="<?= base_url('penerimaan_blt') ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form id="form-add-blt">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="deskripsi">Deskripsi BLT <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="deskripsi" name="deskripsi" rows="3" placeholder="Masukkan deskripsi BLT" required></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="nominal">Nominal BLT <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">Rp</span>
                                        </div>
                                        <input type="number" class="form-control" id="nominal" name="nominal" placeholder="0" min="1" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <label>Pilih Warga Penerima BLT <span class="text-danger">*</span></label>
                                    <div class="card">
                                        <div class="card-header">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input type="checkbox" class="form-check-input" id="select-all">
                                                        <label class="form-check-label" for="select-all">
                                                            <strong>Pilih Semua</strong>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <input type="text" class="form-control form-control-sm" id="search-warga" placeholder="Cari warga...">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                                            <div id="warga-list">
                                                <div class="text-center">
                                                    <div class="spinner-border" role="status">
                                                        <span class="sr-only">Loading...</span>
                                                    </div>
                                                    <p>Memuat data warga...</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Simpan
                                    </button>
                                    <a href="<?= base_url('penerimaan_blt') ?>" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> Batal
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        var wargaData = [];
        var filteredWarga = [];

        // Load warga data
        loadWargaData();

        function loadWargaData() {
            $.ajax({
                url: '<?= base_url('penerimaan_blt/get_warga') ?>',
                type: 'POST',
                dataType: 'json',
                success: function(response) {
                    if (response.RESULT === 'OK') {
                        wargaData = response.DATA;
                        filteredWarga = wargaData;
                        renderWargaList();
                    } else {
                        $('#warga-list').html('<div class="alert alert-danger">Gagal memuat data warga</div>');
                    }
                },
                error: function() {
                    $('#warga-list').html('<div class="alert alert-danger">Terjadi kesalahan saat memuat data warga</div>');
                }
            });
        }

        function renderWargaList() {
            var html = '';
            if (filteredWarga.length === 0) {
                html = '<div class="alert alert-info">Tidak ada data warga yang ditemukan</div>';
            } else {
                filteredWarga.forEach(function(warga) {
                    html += `
                    <div class="warga-item p-3 mb-2 border rounded" data-nik="${warga.nik}">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input warga-checkbox" id="warga-${warga.nik}" name="warga_selected[]" value="${warga.nik}">
                            <label class="form-check-label w-100" for="warga-${warga.nik}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <strong class="d-block">${warga.nama}</strong>
                                        <small class="text-muted">NIK: ${warga.nik}</small><br>
                                        <small class="text-muted">${warga.alamat}, RT ${warga.rt}/RW ${warga.rw}</small>
                                    </div>
                                    <div class="check-indicator">
                                        <i class="fas fa-check text-success" style="display: none;"></i>
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>
                `;
                });
            }
            $('#warga-list').html(html);
        }

        // Search functionality
        $('#search-warga').on('keyup', function() {
            var searchTerm = $(this).val().toLowerCase();
            filteredWarga = wargaData.filter(function(warga) {
                return warga.nama.toLowerCase().includes(searchTerm) ||
                    warga.nik.includes(searchTerm) ||
                    warga.alamat.toLowerCase().includes(searchTerm);
            });
            renderWargaList();

            // Reset select all checkbox
            $('#select-all').prop('checked', false);
        });

        // Select all functionality
        $('#select-all').change(function() {
            var isChecked = $(this).is(':checked');
            $('.warga-checkbox').prop('checked', isChecked);
            updateAllWargaVisual();
        });

        // Update select all when individual checkboxes change
        $(document).on('change', '.warga-checkbox', function() {
            var totalCheckboxes = $('.warga-checkbox').length;
            var checkedCheckboxes = $('.warga-checkbox:checked').length;

            // Update visual feedback
            updateWargaItemVisual($(this));

            if (checkedCheckboxes === totalCheckboxes && totalCheckboxes > 0) {
                $('#select-all').prop('checked', true);
            } else {
                $('#select-all').prop('checked', false);
            }
        });

        // Function to update visual feedback for warga items
        function updateWargaItemVisual(checkbox) {
            var wargaItem = checkbox.closest('.warga-item');
            var checkIcon = wargaItem.find('.check-indicator i');

            if (checkbox.is(':checked')) {
                wargaItem.addClass('selected-warga');
                checkIcon.show();
            } else {
                wargaItem.removeClass('selected-warga');
                checkIcon.hide();
            }
        }

        // Update all visual feedback
        function updateAllWargaVisual() {
            $('.warga-checkbox').each(function() {
                updateWargaItemVisual($(this));
            });
        }

        // Form submission
        $('#form-add-blt').submit(function(e) {
            e.preventDefault();

            var selectedWarga = [];
            $('.warga-checkbox:checked').each(function() {
                selectedWarga.push($(this).val());
            });

            if (selectedWarga.length === 0) {
                swal("Error!", "Pilih minimal satu warga penerima BLT", "error");
                return;
            }

            var formData = {
                deskripsi: $('#deskripsi').val(),
                nominal: $('#nominal').val(),
                warga_selected: selectedWarga
            };

            $.ajax({
                url: '<?= base_url('penerimaan_blt/process_add') ?>',
                type: 'POST',
                data: formData,
                dataType: 'json',
                beforeSend: function() {
                    $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
                },
                success: function(response) {
                    if (response.RESULT === 'OK') {
                        swal("Berhasil!", response.MESSAGE, "success", {
                            timer: 1500
                        });
                        setTimeout(function() {
                            window.location.href = '<?= base_url('penerimaan_blt') ?>';
                        }, 1500);
                    } else {
                        swal("Gagal!", response.MESSAGE, "error");
                        $('button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan');
                    }
                },
                error: function() {
                    swal("Error!", "Terjadi kesalahan saat menyimpan data", "error");
                    $('button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan');
                }
            });
        });

        // Format nominal input
        $('#nominal').on('input', function() {
            var value = $(this).val().replace(/[^0-9]/g, '');
            $(this).val(value);
        });
    }
</script>

<style>
    .card-tools {
        margin-left: auto;
    }

    .form-check-label {
        cursor: pointer;
    }

    .warga-checkbox {
        cursor: pointer;
    }

    #warga-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .warga-item {
        transition: all 0.3s ease;
        cursor: pointer;
        background-color: #fff;
    }

    .warga-item:hover {
        background-color: #f8f9fa;
        border-color: #007bff !important;
    }

    .warga-item.selected-warga {
        background-color: #e3f2fd;
        border-color: #2196f3 !important;
        border-width: 2px !important;
    }

    .form-check-input {
        transform: scale(1.2);
        margin-top: 0.2rem;
    }

    .check-indicator {
        margin-left: 10px;
    }

    .check-indicator i {
        font-size: 1.2rem;
    }

    .form-check-label {
        width: 100%;
        margin-bottom: 0;
    }
</style>