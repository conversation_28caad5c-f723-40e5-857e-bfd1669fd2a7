<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Master_Users $msusers
 * @property KabKota $kabkota
 * @property CI_Upload $upload
 */
class RegencyController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Master_Users', 'msusers');
        $this->load->model('KabKota', 'kabkota');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Kabupaten';
        $data['content'] = 'kabupaten/index';
        $data['kabupaten'] = $this->msusers->select('c.id_kabkota, c.nama_kabkota, c.logo, c.siades')
            ->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
            ->join('kabkota c', 'c.id_kabkota = b.id_kabkota')
            ->group_by('c.id_kabkota, c.nama_kabkota, c.logo')
            ->order_by('c.nama_kabkota', 'ASC')
            ->result();

        return $this->load->view('master', $data);
    }

    public function edit()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Silahkan login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->kabkota->get(array(
            'id_kabkota' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('kabupaten/edit', array(
                'kabkota' => $row,
            ), true)
        ));
    }

    public function process_edit()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Silahkan login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id_kabkota');

        $get = $this->kabkota->get(array(
            'id_kabkota' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        try {
            $upload = doUpload_CloudStorage('logo', 'jpg|png|jpeg');
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }

        $this->kabkota->update(array(
            'id_kabkota' => $id
        ), array(
            'logo' => $upload['name']
        ));

        return JSONResponseDefault('OK', 'Logo berhasil diubah');
    }

    public function set_integration()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Silahkan login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');
        $status = getPost('status');

        if ($id == null) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $get = $this->kabkota->get(array(
            'id_kabkota' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $this->kabkota->update(array(
            'id_kabkota' => $id
        ), array(
            'siades' => $status
        ));

        return JSONResponseDefault('OK', 'Status integrasi berhasil diubah');
    }

    // public function set_siades()
    // {
    //     if (!isLogin()) {
    //         return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
    //     } else if (!isSuperAdmin()) {
    //         return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
    //     }

    //     $id = getPost('id');
    //     $status = getPost('status');

    //     $get = $this->kabkota->get(array('id_kabkota' => $id));

    //     if ($get->num_rows() == 0) {
    //         return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
    //     }

    //     $update = $this->kabkota->update(array(
    //         'id_kabkota' => $id
    //     ), array(
    //         'is_siades' => $status == 1 ? 1 : 0
    //     ));

    //     if ($update) {
    //         return JSONResponseDefault('OK', 'Data berhasil diubah');
    //     } else {
    //         return JSONResponseDefault('FAILED', 'Gagal mengubah data');
    //     }
    // }
}
