<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>

<!-- Hero Section -->
<section class="relative h-96 bg-cover bg-center"
    style="background-image: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80');">
    <div class="absolute inset-0 flex items-center justify-center">
        <div class="text-center text-white max-w-4xl mx-auto px-4">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                Pemerintahan Desa
            </h1>
            <p class="text-xl md:text-2xl font-light">
                <?= isset($setting->desa) ? $setting->desa : 'Desa' ?>
            </p>
        </div>
    </div>
</section>

<!-- Vision Mission Section -->
<?php if (isset($pemerintahan) && ($pemerintahan->visi || $pemerintahan->misi)) : ?>
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <p class="text-primary font-medium mb-2">Visi & Misi</p>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800">
                    Visi dan Misi <?= isset($setting->desa) ? $setting->desa : 'Desa' ?>
                </h2>
            </div>

            <div class="grid md:grid-cols-2 gap-12 max-w-6xl mx-auto">
                <?php if ($pemerintahan->visi) : ?>
                    <!-- Visi -->
                    <div class="bg-gradient-to-br from-primary to-primary-dark p-8 rounded-2xl text-white">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-white rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-eye text-primary text-xl"></i>
                            </div>
                            <h3 class="text-2xl font-bold">Visi</h3>
                        </div>
                        <p class="text-lg leading-relaxed">
                            <?= $pemerintahan->visi ?>
                        </p>
                    </div>
                <?php endif; ?>

                <?php if ($pemerintahan->misi) : ?>
                    <!-- Misi -->
                    <div class="bg-gray-50 p-8 rounded-2xl">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-target text-white text-xl"></i>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-800">Misi</h3>
                        </div>
                        <div class="text-gray-600 leading-relaxed">
                            <?= $pemerintahan->misi ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>
<?php endif; ?>

<!-- Organizational Structure -->
<?php if (isset($strukturorganisasi) && count($strukturorganisasi) > 0) : ?>
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <p class="text-primary font-medium mb-2">Struktur Organisasi</p>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800">Perangkat Desa</h2>
            </div>

            <!-- Organizational Chart -->
            <?php if (isset($pemerintahan->struktur) && !empty($pemerintahan->struktur)) : ?>
                <div class="mb-12 text-center">
                    <div class="bg-white rounded-2xl shadow-lg p-8 inline-block">
                        <img src="<?= asset_url($pemerintahan->struktur) ?>"
                            alt="Struktur Organisasi"
                            class="max-w-full h-auto rounded-lg">
                    </div>
                </div>
            <?php endif; ?>

            <!-- Staff Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php foreach ($strukturorganisasi as $staff) : ?>
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 officer-card">
                        <div class="relative">
                            <?php if ($staff->foto) : ?>
                                <img src="<?= asset_url($staff->foto) ?>"
                                    alt="<?= $staff->nama ?>"
                                    class="w-full h-64 object-cover officer-image">
                            <?php else : ?>
                                <div class="w-full h-64 bg-gray-200 flex items-center justify-center">
                                    <i class="fas fa-user text-gray-400 text-6xl"></i>
                                </div>
                            <?php endif; ?>

                            <!-- Badge for position -->
                            <div class="absolute top-4 left-4">
                                <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                                    <?= $staff->jabatan ?>
                                </span>
                            </div>
                        </div>

                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-800 mb-2"><?= $staff->nama ?></h3>
                            <p class="text-primary font-medium mb-3"><?= $staff->jabatan ?></p>

                            <?php if (isset($staff->tugas) && $staff->tugas) : ?>
                                <p class="text-gray-600 text-sm mb-4 line-clamp-3">
                                    <?= character_limiter($staff->tugas, 100) ?>
                                </p>
                            <?php endif; ?>

                            <!-- Contact Info -->
                            <div class="space-y-2">
                                <?php if (isset($staff->email) && $staff->email) : ?>
                                    <div class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-envelope mr-2 text-primary"></i>
                                        <span><?= $staff->email ?></span>
                                    </div>
                                <?php endif; ?>

                                <?php if (isset($staff->telepon) && $staff->telepon) : ?>
                                    <div class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-phone mr-2 text-primary"></i>
                                        <span><?= $staff->telepon ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Detail Button -->
                            <button onclick="detailStaff(<?= $staff->id ?>)"
                                class="mt-4 w-full bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-lg font-medium transition-colors duration-300">
                                Lihat Detail
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
<?php endif; ?>

<!-- Government Services -->
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <p class="text-primary font-medium mb-2">Layanan Pemerintahan</p>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-800">Pelayanan Publik</h2>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Service 1 -->
            <div class="text-center group">
                <div class="w-20 h-20 bg-primary rounded-2xl mx-auto mb-4 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-file-alt text-white text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Pelayanan Surat</h3>
                <p class="text-gray-600 text-sm">Pembuatan surat keterangan dan administrasi desa</p>
            </div>

            <!-- Service 2 -->
            <div class="text-center group">
                <div class="w-20 h-20 bg-primary rounded-2xl mx-auto mb-4 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-users text-white text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Kependudukan</h3>
                <p class="text-gray-600 text-sm">Layanan administrasi kependudukan dan catatan sipil</p>
            </div>

            <!-- Service 3 -->
            <div class="text-center group">
                <div class="w-20 h-20 bg-primary rounded-2xl mx-auto mb-4 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-home text-white text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Perizinan</h3>
                <p class="text-gray-600 text-sm">Pengurusan izin usaha dan perizinan lainnya</p>
            </div>

            <!-- Service 4 -->
            <div class="text-center group">
                <div class="w-20 h-20 bg-primary rounded-2xl mx-auto mb-4 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-handshake text-white text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Pemberdayaan</h3>
                <p class="text-gray-600 text-sm">Program pemberdayaan masyarakat dan ekonomi</p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Information -->
<section class="py-16 bg-primary text-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">Hubungi Kami</h2>
            <p class="text-xl text-white/80">Kami siap melayani Anda</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <!-- Address -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-full mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-map-marker-alt text-primary text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">Alamat</h3>
                <p class="text-white/80">
                    <?= isset($setting->alamat) ? $setting->alamat : 'Alamat Kantor Desa' ?>
                </p>
            </div>

            <!-- Phone -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-full mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-phone text-primary text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">Telepon</h3>
                <p class="text-white/80">
                    <?= isset($setting->telepon) ? $setting->telepon : '-' ?>
                </p>
            </div>

            <!-- Email -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-full mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-envelope text-primary text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">Email</h3>
                <p class="text-white/80">
                    <?= isset($setting->email) ? $setting->email : '-' ?>
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Modal for Staff Detail -->
<div id="staffModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-gray-800">Detail Perangkat Desa</h3>
                <button onclick="closeStaffModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-2xl"></i>
                </button>
            </div>
            <div id="staffModalContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
    function detailStaff(id) {
        // Show modal
        document.getElementById('staffModal').classList.remove('hidden');

        // Load staff detail via AJAX
        fetch('<?= base_url('pemerintah/detail') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'id=' + id
            })
            .then(response => response.json())
            .then(data => {
                if (data.RESULT === 'OK') {
                    document.getElementById('staffModalContent').innerHTML = data.CONTENT;
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
    }

    function closeStaffModal() {
        document.getElementById('staffModal').classList.add('hidden');
    }

    // Close modal when clicking outside
    document.getElementById('staffModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeStaffModal();
        }
    });
</script>