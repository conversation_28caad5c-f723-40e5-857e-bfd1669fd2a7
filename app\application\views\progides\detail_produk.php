<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>

<!-- Breadcrumb -->
<section class="py-8 bg-gray-100 mt-20">
    <div class="container mx-auto px-4">
        <nav class="flex items-center space-x-2 text-sm">
            <a href="<?= base_url() ?>" class="text-primary hover:text-primary-dark">Beranda</a>
            <i class="fas fa-chevron-right text-gray-400"></i>
            <a href="<?= base_url('belanja') ?>" class="text-primary hover:text-primary-dark">Belanja</a>
            <i class="fas fa-chevron-right text-gray-400"></i>
            <span class="text-gray-600">Detail Produk</span>
        </nav>
    </div>
</section>

<!-- Product Detail -->
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Product Images -->
            <div class="space-y-4">
                <!-- Main Image -->
                <div class="relative">
                    <img id="mainImage"
                        src="<?= asset_url($produk->foto) ?>"
                        alt="<?= $produk->nama ?>"
                        class="w-full h-96 object-cover rounded-2xl shadow-lg">

                    <!-- Category Badge -->
                    <div class="absolute top-4 left-4">
                        <span class="bg-primary text-white px-4 py-2 rounded-full font-medium">
                            <?= $produk->kategori ?>
                        </span>
                    </div>
                </div>

                <!-- Thumbnail Images -->
                <?php if (isset($detail_produk) && count($detail_produk) > 0) : ?>
                    <div class="grid grid-cols-4 gap-4">
                        <img src="<?= asset_url($produk->foto) ?>"
                            alt="<?= $produk->nama ?>"
                            class="thumbnail w-full h-20 object-cover rounded-lg cursor-pointer border-2 border-primary"
                            onclick="changeMainImage('<?= asset_url($produk->foto) ?>', this)">

                        <?php foreach (array_slice($detail_produk, 0, 3) as $detail) : ?>
                            <img src="<?= asset_url($detail->foto) ?>"
                                alt="<?= $produk->nama ?>"
                                class="thumbnail w-full h-20 object-cover rounded-lg cursor-pointer border-2 border-transparent hover:border-primary transition-colors duration-300"
                                onclick="changeMainImage('<?= asset_url($detail->foto) ?>', this)">
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Product Info -->
            <div class="space-y-6">
                <div>
                    <h1 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                        <?= $produk->nama ?>
                    </h1>

                    <div class="flex items-center space-x-4 text-sm text-gray-600 mb-6">
                        <span class="flex items-center">
                            <i class="fas fa-tag mr-2 text-primary"></i>
                            <?= $produk->kategori ?>
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-calendar mr-2 text-primary"></i>
                            <?= date('d M Y', strtotime($produk->createddate)) ?>
                        </span>
                    </div>
                </div>

                <!-- Description -->
                <div class="prose max-w-none">
                    <h3 class="text-xl font-semibold text-gray-800 mb-3">Deskripsi Produk</h3>
                    <div class="text-gray-600 leading-relaxed">
                        <?= nl2br($produk->deskripsi) ?>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="bg-gray-50 p-6 rounded-2xl">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">Informasi Kontak</h3>

                    <?php if ($produk->no_telp) : ?>
                        <div class="flex items-center mb-3">
                            <i class="fas fa-phone text-primary mr-3"></i>
                            <span class="text-gray-700"><?= $produk->no_telp ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($produk->alamat) && $produk->alamat) : ?>
                        <div class="flex items-center mb-3">
                            <i class="fas fa-map-marker-alt text-primary mr-3"></i>
                            <span class="text-gray-700"><?= $produk->alamat ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($produk->email) && $produk->email) : ?>
                        <div class="flex items-center">
                            <i class="fas fa-envelope text-primary mr-3"></i>
                            <span class="text-gray-700"><?= $produk->email ?></span>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Action Buttons -->
                <div class="space-y-4">
                    <?php if ($produk->no_telp) : ?>
                        <button onclick="pesanWhatsApp('<?= $produk->no_telp ?>', '<?= $produk->nama ?>')"
                            class="w-full bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-colors duration-300 flex items-center justify-center">
                            <i class="fab fa-whatsapp mr-3 text-xl"></i>
                            Pesan via WhatsApp
                        </button>
                    <?php endif; ?>

                    <button onclick="copyToClipboard(window.location.href)"
                        class="w-full bg-gray-200 hover:bg-gray-300 text-gray-800 px-8 py-4 rounded-xl font-semibold transition-colors duration-300 flex items-center justify-center">
                        <i class="fas fa-share mr-3"></i>
                        Bagikan Produk
                    </button>
                </div>

                <!-- Social Share -->
                <div class="border-t pt-6">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">Bagikan ke:</h4>
                    <div class="flex space-x-4">
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?= current_url() ?>"
                            target="_blank"
                            class="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors duration-300">
                            <i class="fab fa-facebook-f"></i>
                        </a>

                        <a href="https://twitter.com/intent/tweet?url=<?= current_url() ?>&text=<?= urlencode($produk->nama) ?>"
                            target="_blank"
                            class="w-12 h-12 bg-blue-400 text-white rounded-full flex items-center justify-center hover:bg-blue-500 transition-colors duration-300">
                            <i class="fab fa-twitter"></i>
                        </a>

                        <a href="https://wa.me/?text=<?= urlencode($produk->nama . ' - ' . current_url()) ?>"
                            target="_blank"
                            class="w-12 h-12 bg-green-500 text-white rounded-full flex items-center justify-center hover:bg-green-600 transition-colors duration-300">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Products -->
<?php if (isset($other) && count($other) > 0) : ?>
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-gray-800 mb-12 text-center">Produk Lainnya</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <?php foreach (array_slice($other, 0, 4) as $item) : ?>
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                        <div class="relative">
                            <img src="<?= asset_url($item->foto) ?>"
                                alt="<?= $item->nama ?>"
                                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">

                            <div class="absolute top-4 left-4">
                                <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                                    <?= $item->kategori ?>
                                </span>
                            </div>
                        </div>

                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">
                                <?= $item->nama ?>
                            </h3>

                            <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                                <?= isset($item->deskripsi) && $item->deskripsi !== null ? character_limiter($item->deskripsi, 80) : 'Tidak ada deskripsi' ?>
                            </p>

                            <div class="flex gap-2">
                                <a href="<?= base_url('guest/product_detail/' . $item->id) ?>"
                                    class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-lg text-sm font-medium text-center transition-colors duration-300">
                                    Detail
                                </a>
                                <?php if ($item->no_telp) : ?>
                                    <button onclick="pesanWhatsApp('<?= $item->no_telp ?>', '<?= $item->nama ?>')"
                                        class="flex-1 bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-300">
                                        Pesan
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
<?php endif; ?>

<!-- Back to Products -->
<section class="py-8 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center">
            <a href="<?= base_url('belanja') ?>"
                class="inline-flex items-center bg-primary hover:bg-primary-dark text-white px-8 py-3 rounded-full font-medium transition-colors duration-300">
                <i class="fas fa-arrow-left mr-2"></i>
                Kembali ke Belanja
            </a>
        </div>
    </div>
</section>

<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .prose h3 {
        color: #1f2937;
        font-weight: 600;
    }

    .thumbnail.active {
        border-color: #6e9e28;
    }
</style>

<script>
    function changeMainImage(src, element) {
        document.getElementById('mainImage').src = src;

        // Remove active class from all thumbnails
        document.querySelectorAll('.thumbnail').forEach(thumb => {
            thumb.classList.remove('border-primary');
            thumb.classList.add('border-transparent');
        });

        // Add active class to clicked thumbnail
        element.classList.remove('border-transparent');
        element.classList.add('border-primary');
    }

    function pesanWhatsApp(phone, productName) {
        const message = `Halo, saya tertarik dengan produk "${productName}". Bisakah Anda memberikan informasi lebih lanjut mengenai harga dan ketersediaan?`;
        const whatsappUrl = `https://wa.me/${phone}?text=${encodeURIComponent(message)}`;
        window.open(whatsappUrl, '_blank');
    }

    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            // Show success message
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check mr-3"></i>Link Disalin!';
            button.classList.remove('bg-gray-200', 'hover:bg-gray-300', 'text-gray-800');
            button.classList.add('bg-green-500', 'hover:bg-green-600', 'text-white');

            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('bg-green-500', 'hover:bg-green-600', 'text-white');
                button.classList.add('bg-gray-200', 'hover:bg-gray-300', 'text-gray-800');
            }, 2000);
        }).catch(function(err) {
            console.error('Could not copy text: ', err);
        });
    }
</script>