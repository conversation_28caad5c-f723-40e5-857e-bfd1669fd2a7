<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Master_Users $users
 */
class DiskominfoController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Master_Users', 'users');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Manage Diskominfo';
        $data['content'] = 'diskominfo/index';
        $data['diskominfo'] = $this->users->result(array(
            'role' => 'diskominfo'
        ));

        $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Diskominfo';
        $data['content'] = 'diskominfo/add';

        $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $username = getPost('username');
        $password = getPost('password');

        if ($username == null) {
            return JSONResponseDefault('FAILED', 'Username tidak boleh kosong');
        } else if ($password == null) {
            return JSONResponseDefault('FAILED', 'Password tidak boleh kosong');
        }

        $get = $this->users->get(array(
            'username' => $username
        ));

        if ($get->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Username sudah digunakan');
        }

        $insert = array();
        $insert['username'] = $username;
        $insert['password'] = md5($password);
        $insert['role'] = 'diskominfo';
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $this->users->insert($insert);

        return JSONResponseDefault('OK', 'Berhasil menambahkan data');
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        if ($id == null) {
            return JSONResponseDefault('FAILED', 'ID tidak boleh kosong');
        }

        $get = $this->users->get(array(
            'id' => $id,
            'role' => 'diskominfo'
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $this->users->delete(array('id' => $id));

        return JSONResponseDefault('OK', 'Berhasil menghapus data');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->users->get(array(
            'id' => $id,
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/diskominfo'));
        }

        $data = array();
        $data['title'] = 'Edit Diskominfo';
        $data['content'] = 'diskominfo/edit';
        $data['diskominfo'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->users->get(array(
            'id' => $id,
            'role' => 'diskominfo'
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        $username = getPost('username');
        $password = getPost('password');

        if ($username == null) {
            return JSONResponseDefault('FAILED', 'Username tidak boleh kosong');
        }

        if ($row->username != $username) {
            $get = $this->users->get(array(
                'username' => $username
            ));

            if ($get->num_rows() > 0) {
                return JSONResponseDefault('FAILED', 'Username sudah digunakan');
            }
        }

        $update = array();
        $update['username'] = $username;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        if ($password != null) {
            $update['password'] = md5($password);
        }

        $this->users->update(array('id' => $id), $update);

        return JSONResponseDefault('OK', 'Berhasil mengubah data');
    }
}
