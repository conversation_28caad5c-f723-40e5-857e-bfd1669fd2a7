<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Master_Users $masterusers
 */
class MasterController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Master_Users', 'masterusers');
    }

    public function select_kabupaten()
    {
        $provinsi = getPost('provinsi');
        $select = getPost('select');

        $kabupaten = $this->db->get_where('kabkota', array(
            'id_propinsi' => $provinsi
        ))->result();

        $html = "";
        foreach ($kabupaten as $key => $value) {
            $html .= "<option value='" . $value->id_kabkota . "' " . ($select == $value->id_kabkota ? 'selected' : null) . ">" . $value->nama_kabkota . "</option>";
        }

        echo $html;
    }

    public function select_kecamatan()
    {
        $kabupaten = getPost('kabupaten');
        $select = getPost('select');
        $select = explode(',', $select ?? '');

        $kecamatan = $this->db->get_where('kecamatan', array(
            'id_kabkota' => $kabupaten
        ))->result();

        $html = "";
        foreach ($kecamatan as $key => $value) {
            $html .= "<option value='" . $value->id_kecamatan . "' " . (in_array($value->id_kecamatan, $select) ? 'selected' : null) . ">" . $value->nama_kecamatan . "</option>";
        }

        echo $html;
    }

    public function select_kelurahan()
    {
        $kecamatan = getPost('kecamatan');
        $select = getPost('select');

        $kelurahan = $this->db->get_where('kelurahan', array(
            'id_kecamatan' => $kecamatan
        ))->result();

        $html = "";
        foreach ($kelurahan as $key => $value) {
            $html .= "<option value='" . $value->id_kelurahan . "' " . ($select == $value->id_kelurahan ? 'selected' : null) . ">" . $value->nama_kelurahan . "</option>";
        }

        echo $html;
    }

    public function select_kelurahandesa()
    {
        $desaid = getPost('desaid');
        $kecamatanid = getPost('kecamatanid');

        $userid = array();
        if (isPMD()) {
            $user = $this->masterusers->getDefaultData(array('id' => getCurrentIdUser()))->row();

            $kecamatan = $user->kecamatanid;

            if ($kecamatan == null) {
                $kelurahan = $this->db->get_where('kelurahan', array('id_kabkota' => $user->kabkotaid))->result();
            } else {
                $kecamatan = explode(',', $kecamatan);
                $kelurahan = $this->db->where_in('id_kecamatan', $kecamatan)->get_where('kelurahan', array('id_kabkota' => $user->kabkotaid))->result();
            }

            $kelurahanid = array();
            foreach ($kelurahan as $key => $value) {
                $kelurahanid[] = $value->id_kelurahan;
            }

            $users = $this->db->where_in('kelurahanid', $kelurahanid)->get('msusers')->result();

            foreach ($users as $key => $value) {
                $userid[] = $value->id;
            }
        }

        $this->masterusers->select('a.*, b.nama_kelurahan')
            ->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
            ->join('kecamatan c', 'c.id_kecamatan = b.id_kecamatan')
            ->order_by('b.nama_kelurahan');

        if (count($userid) > 0) {
            $this->masterusers->where_in('a.id', $userid);
        }

        if ($kecamatanid != null) {
            $this->masterusers->where(array(
                'c.id_kecamatan' => $kecamatanid
            ));
        }

        if (isSuperAdmin() && !isAllPlatform()) {
            $this->masterusers->where(array(
                'a.platformname' => getCurrentPlatformName()
            ));
        }

        $kelurahan = $this->masterusers->result();

        $html = "<option value=\"\">- Semua -</option>";;
        foreach ($kelurahan as $key => $value) {
            $html .= "<option value=\"$value->id\" " . ($value->id == $desaid ? 'selected' : null) . ">$value->nama_kelurahan</option>";
        }

        echo $html;
    }

    public function select_kecamatandesa()
    {
        $kecamatanid = getPost('kecamatanid');
        $kabupatenid = getPost('kabupatenid');

        $userid = array();
        if (isPMD()) {
            $user = $this->masterusers->getDefaultData(array('id' => getCurrentIdUser()))->row();

            $kecamatan = $user->kecamatanid;

            if ($kecamatan == null) {
                $kelurahan = $this->db->get_where('kelurahan', array('id_kabkota' => $user->kabkotaid))->result();
            } else {
                $kecamatan = explode(',', $kecamatan);
                $kelurahan = $this->db->where_in('id_kecamatan', $kecamatan)->get_where('kelurahan', array('id_kabkota' => $user->kabkotaid))->result();
            }

            $kelurahanid = array();
            foreach ($kelurahan as $key => $value) {
                $kelurahanid[] = $value->id_kelurahan;
            }

            $users = $this->db->where_in('kelurahanid', $kelurahanid)->get('msusers')->result();

            foreach ($users as $key => $value) {
                $userid[] = $value->id;
            }
        }

        $this->masterusers->select('c.id_kecamatan, c.nama_kecamatan')
            ->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid', 'LEFT')
            ->join('kecamatan c', 'c.id_kecamatan = b.id_kecamatan OR c.id_kecamatan = a.kecamatanid', 'LEFT')
            ->where('c.id_kecamatan IS NOT NULL')
            ->order_by('c.nama_kecamatan')
            ->group_by('c.id_kecamatan, c.nama_kecamatan');

        if (count($userid) > 0) {
            $this->masterusers->where_in('a.id', $userid);
        }

        if ($kabupatenid != null) {
            $this->masterusers->where(array(
                "(c.id_kabkota = $kabupatenid OR a.kabkotaid = $kabupatenid) =" => true
            ));
        }

        if (isSuperAdmin() && !isAllPlatform()) {
            $this->masterusers->where(array(
                'a.platformname' => getCurrentPlatformName()
            ));
        }

        $kecamatan = $this->masterusers->result();

        $html = "<option value=\"\">- Semua -</option>";
        foreach ($kecamatan as $key => $value) {
            $html .= "<option value=\"$value->id_kecamatan\" " . ($kecamatanid == $value->id_kecamatan ? 'selected' : null) . ">$value->nama_kecamatan</option>";
        }

        echo $html;
    }
}
