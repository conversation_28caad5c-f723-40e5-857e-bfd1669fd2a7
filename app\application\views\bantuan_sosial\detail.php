<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>

<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 mb-3">
                        <li class="breadcrumb-item">
                            <a href="<?= base_url() ?>" class="text-white-50">Home</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('bantuan-sosial') ?>" class="text-white-50">Bantuan Sosial</a>
                        </li>
                        <li class="breadcrumb-item active text-white" aria-current="page">Detail</li>
                    </ol>
                </nav>
                <h1 class="hero-title"><?= $penerimaan_blt->deskripsi ?></h1>
                <p class="hero-subtitle">
                    <i class="fas fa-money-bill-wave"></i>
                    Nominal: Rp <?= number_format($penerimaan_blt->nominal, 0, ',', '.') ?>
                </p>
                <p class="hero-subtitle">
                    <i class="fas fa-calendar"></i>
                    Dibuat: <?= date('d F Y', strtotime($penerimaan_blt->tanggal_dibuat)) ?>
                </p>
            </div>
            <div class="col-lg-4">
                <div class="hero-image">
                    <i class="fas fa-users fa-8x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="content-section">
    <div class="container">
        <!-- Summary Stats -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <h3 class="text-primary mb-2"><?= count($penerima) ?></h3>
                                    <p class="text-muted mb-0">Total Penerima Terverifikasi</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <h3 class="text-success mb-2">Rp <?= number_format($penerimaan_blt->nominal * count($penerima), 0, ',', '.') ?></h3>
                                    <p class="text-muted mb-0">Total Dana Tersalurkan</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <h3 class="text-info mb-2">Rp <?= number_format($penerimaan_blt->nominal, 0, ',', '.') ?></h3>
                                    <p class="text-muted mb-0">Nominal per Penerima</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Daftar Penerima -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i>
                            Daftar Penerima Bantuan Sosial
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($penerima)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="5%">No</th>
                                            <th width="20%">NIK</th>
                                            <th width="25%">Nama</th>
                                            <th width="35%">Alamat</th>
                                            <th width="15%">RT/RW</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($penerima as $key => $value): ?>
                                            <tr>
                                                <td><?= $key + 1 ?></td>
                                                <td>
                                                    <span class="badge bg-secondary"><?= $value->nik ?></span>
                                                </td>
                                                <td>
                                                    <strong><?= $value->nama ?></strong>
                                                </td>
                                                <td><?= $value->alamat ?></td>
                                                <td>
                                                    <span class="badge bg-info">RT <?= $value->rt ?> / RW <?= $value->rw ?></span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-users-slash fa-5x text-muted mb-3"></i>
                                <h4 class="text-muted">Belum Ada Penerima Terverifikasi</h4>
                                <p class="text-muted">Saat ini belum ada penerima yang terverifikasi untuk program bantuan sosial ini.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Info Section -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card bg-light">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-lg-8">
                                <h6 class="mb-2">
                                    <i class="fas fa-info-circle text-primary"></i>
                                    Catatan Penting
                                </h6>
                                <p class="mb-0 text-muted">
                                    Data yang ditampilkan adalah penerima bantuan sosial yang telah melalui proses verifikasi dan dinyatakan layak menerima bantuan. 
                                    Jika ada pertanyaan atau keberatan, silakan hubungi kantor desa.
                                </p>
                            </div>
                            <div class="col-lg-4 text-lg-end">
                                <a href="<?= base_url('bantuan-sosial') ?>" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-arrow-left"></i>
                                    Kembali
                                </a>
                                <a href="<?= base_url('guestbook') ?>" class="btn btn-primary">
                                    <i class="fas fa-phone"></i>
                                    Hubungi Kami
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 100px 0 80px;
    margin-top: 70px;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.hero-image {
    text-align: center;
}

.content-section {
    padding: 80px 0;
}

.stat-item h3 {
    font-size: 2.5rem;
    font-weight: 600;
}

.card {
    border: none;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.8rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: rgba(255, 255, 255, 0.5);
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 1.8rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .stat-item h3 {
        font-size: 2rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}
</style>
