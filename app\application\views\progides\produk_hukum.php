<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>

<!-- Hero Section -->
<section class="relative h-96 bg-cover bg-center"
    style="background-image: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1589829545856-d10d557cf95f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');">
    <div class="absolute inset-0 flex items-center justify-center">
        <div class="text-center text-white max-w-4xl mx-auto px-4">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                Produk Hu<PERSON>m
            </h1>
            <p class="text-xl md:text-2xl font-light">
                Dokumen Hukum dan <PERSON> <?= isset($setting->desa) ? $setting->desa : 'Desa' ?>
            </p>
        </div>
    </div>
</section>

<!-- Main Content -->
<div class="min-h-screen bg-gray-50 py-16">
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row gap-12">
            <!-- Left Side - Logo -->
            <div class="lg:w-1/3">
                <div class="bg-white rounded-xl shadow-lg p-8 text-center sticky top-8">
                    <h2 class="text-2xl font-bold text-gray-800 mb-8">PRODUK HUKUM</h2>
                    <div class="flex justify-center mb-8">
                        <div class="w-48 h-48 bg-white rounded-full flex items-center justify-center shadow-lg border border-gray-100">
                            <!-- Logo Desa -->
                            <div class="text-center">
                                <div class="w-16 h-16 bg-primary rounded-lg mx-auto mb-3 flex items-center justify-center">
                                    <i class="fas fa-landmark text-white text-2xl"></i>
                                </div>
                                <div class="text-primary text-sm font-bold leading-tight">
                                    <?= isset($setting->desa) ? strtoupper(str_replace(' ', '<br>', $setting->desa)) : 'DESA' ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="text-gray-600 text-sm text-center">
                        <p class="mb-2">Kumpulan dokumen hukum dan peraturan</p>
                        <p>yang berlaku di <?= isset($setting->desa) ? $setting->desa : 'Desa' ?></p>
                    </div>
                </div>
            </div>

            <!-- Right Side - Documents Grid -->
            <div class="lg:w-2/3">
                <div class="mb-8">
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">Kategori Dokumen Hukum</h3>
                    <p class="text-gray-600">Pilih kategori dokumen yang ingin Anda akses</p>
                </div>

                <?php if (!empty($kategori)) : ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <?php
                        $colors = [
                            'from-red-500 to-red-600',
                            'from-blue-500 to-blue-600',
                            'from-green-500 to-green-600',
                            'from-purple-500 to-purple-600',
                            'from-orange-500 to-orange-600',
                            'from-gray-500 to-gray-600'
                        ];
                        $icons = [
                            'fas fa-file-alt',
                            'fas fa-file-contract',
                            'fas fa-gavel',
                            'fas fa-file-signature',
                            'fas fa-users',
                            'fas fa-balance-scale'
                        ];
                        ?>
                        <?php foreach ($kategori as $index => $kat) : ?>
                            <div class="bg-white rounded-xl shadow-lg p-8 text-center hover:shadow-2xl transition duration-300 transform hover:-translate-y-1 cursor-pointer border border-gray-100"
                                onclick="showDocuments(<?= $kat->id ?>)">
                                <div class="w-20 h-20 bg-gradient-to-br <?= $colors[$index % count($colors)] ?> rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg">
                                    <i class="<?= $icons[$index % count($icons)] ?> text-white text-3xl"></i>
                                </div>
                                <h3 class="font-bold text-gray-800 text-lg mb-2"><?= strtoupper($kat->name) ?></h3>
                                <p class="text-gray-500 text-sm"><?= isset($kat->description) ? $kat->description : 'Dokumen kategori ' . $kat->name ?></p>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else : ?>
                    <div class="bg-white rounded-xl shadow-lg p-12 text-center">
                        <div class="w-20 h-20 bg-gray-100 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                            <i class="fas fa-folder-open text-gray-400 text-3xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Belum Ada Kategori</h3>
                        <p class="text-gray-600">Kategori dokumen hukum belum tersedia.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Documents -->
<div id="documentsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
        <div class="p-6 border-b border-gray-200 flex justify-between items-center">
            <h3 id="modalTitle" class="text-xl font-bold text-gray-800">Dokumen</h3>
            <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div id="modalContent" class="p-6 overflow-y-auto max-h-[60vh]">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>

<script>
    function showDocuments(categoryId) {
        // Show loading
        document.getElementById('modalContent').innerHTML = `
        <div class="text-center py-8">
            <i class="fas fa-spinner fa-spin text-3xl text-primary mb-4"></i>
            <p class="text-gray-600">Memuat dokumen...</p>
        </div>
    `;

        // Show modal
        document.getElementById('documentsModal').classList.remove('hidden');

        // Fetch documents
        fetch('<?= base_url('produk/hukum/detail') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'id=' + categoryId
            })
            .then(response => response.json())
            .then(data => {
                if (data.RESULT === 'OK') {
                    document.getElementById('modalContent').innerHTML = data.CONTENT;
                } else {
                    document.getElementById('modalContent').innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-3xl text-yellow-500 mb-4"></i>
                    <p class="text-gray-600">Gagal memuat dokumen.</p>
                </div>
            `;
                }
            })
            .catch(error => {
                document.getElementById('modalContent').innerHTML = `
            <div class="text-center py-8">
                <i class="fas fa-exclamation-triangle text-3xl text-red-500 mb-4"></i>
                <p class="text-gray-600">Terjadi kesalahan saat memuat dokumen.</p>
            </div>
        `;
            });
    }

    function closeModal() {
        document.getElementById('documentsModal').classList.add('hidden');
    }

    // Close modal when clicking outside
    document.getElementById('documentsModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });
</script>