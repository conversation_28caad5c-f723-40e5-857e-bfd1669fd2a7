<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Infografis_Model $infografis
 * @property Kontak_Penting $kontakpenting
 * @property Setting_Umum $settingumum
 * @property Master_Users $masterusers
 * @property Kategori_InfografisModel $kategori_info
 * @property Master_Infografis $msinfografis
 * @property CI_DB_query_builder $db
 */
class InfoGrafis extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Infografis_Model', 'infografis');
        $this->load->model('Kontak_Penting', 'kontakpenting');
        $this->load->model('Setting_Umum', 'settingumum');
        $this->load->model('Master_Users', 'masterusers');
        $this->load->model('Kategori_InfografisModel', 'kategori_info');
        $this->load->model('Master_Infografis', 'msinfografis');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return redirect('dashboard');
        }

        $where = array();
        if (getSessionValue('ROLE') == 'admin') {
            $where['a.id_user'] = getCurrentIdUser();
        }

        $ingfo = $this->infografis->getDefaultData($where);
        $primary_infografis = $this->kategori_info->result(array('is_primary' => 1));

        $data = array();
        $data['title'] = 'Info Grafis';
        $data['content'] = 'infografis';
        $data['primary_infografis'] = $primary_infografis;
        if ($ingfo->num_rows() > 0) {
            $data['info'] = $ingfo->row();
        } else {
            $data['info'] = null;
        }
        $data['infografis'] = $this->db->query("SELECT a.*, b.nama AS nama_kategori, b.is_primary FROM msinfografis a JOIN kategori_infografis b ON b.id = a.id_kategori WHERE b.is_primary IS NULL AND a.id_user = " . getCurrentIdUser())->result();

        return $this->load->view('master', $data);
    }

    public function process_update()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $laki = getPost('laki');
        $perempuan = getPost('perempuan');

        $ingfo = $this->infografis->getDefaultData(array('id_user' => getCurrentIdUser()));
        $primary_infografis = $this->kategori_info->result(array('is_primary' => 1));

        $exec = array();
        foreach ($primary_infografis as $key => $value) {
            $name = preg_replace('/[^a-z]/i', '', $value->nama);
            $name = strtolower($name);

            if ($name == 'tamatsdsederajat') {
                $exec['sd'] = getPost($name);
            } else if ($name == 'sltpsederajat') {
                $exec['sltp'] = getPost($name);
            } else if ($name == 'tidakbelumsekolah') {
                $exec['belum_sekolah'] = getPost($name);
            } else if ($name == 'sltasederajat') {
                $exec['slta'] = getPost($name);
            } else if ($name == 'belumtamatsdsederajat') {
                $exec['belum_tamat'] = getPost($name);
            } else if ($name == 'diplomaivstarta') {
                $exec['dsi'] = getPost($name);
            } else if ($name == 'diplomaiii') {
                $exec['d1'] = getPost($name);
            } else if ($name == 'diplomaiiisarjanamuda') {
                $exec['d3'] = getPost($name);
            } else if ($name == 'tidakataubelumbekerja') {
                $exec['pengganguran'] = getPost($name);
            } else if ($name == 'mengurusrumahtangga') {
                $exec['ibu_rt'] = getPost($name);
            } else if ($name == 'pelajardanmahasiswa') {
                $exec['pelajar'] = getPost($name);
            } else if ($name == 'pegawainegerisipilpns') {
                $exec['pns'] = getPost($name);
            } else if ($name == 'karyawanswasta') {
                $exec['karyawan_swasta'] = getPost($name);
            } else if ($name == 'wiraswasta') {
                $exec['wiraswasta'] = getPost($name);
            } else if ($name == 'buruhharianlepas') {
                $exec['buruh_harian'] = getPost($name);
            } else if ($name == 'lainlainnya') {
                $get = $this->msinfografis->get(array(
                    'a.id_kategori' => $value->id,
                    'a.id_user' => getCurrentIdUser()
                ));

                if ($get->num_rows() > 0) {
                    $update = array();
                    $update['jumlah'] = getPost($name);
                    $this->msinfografis->update(array('id' => $get->row()->id), $update);
                } else {
                    $insert = array();
                    $insert['id_user'] = getCurrentIdUser();
                    $insert['id_kategori'] = $value->id;
                    $insert['jumlah'] = getPost($name);
                    $this->msinfografis->insert($insert);
                }
            }
        }

        $exec['laki'] = $laki;
        $exec['perempuan'] = $perempuan;
        $exec['id_user'] = getCurrentIdUser();

        if ($ingfo->num_rows() == 0) {
            $doExecute = $this->infografis->insert($exec);
        } else {
            $doExecute = $this->infografis->update(array('id_user' => getCurrentIdUser()), $exec);
        }

        if ($doExecute) {
            return JSONResponseDefault('OK', 'Infografis berhasil disimpan');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menyimpan infografis');
        }
    }

    public function infografi()
    {
        $subdomainaccount_id = $this->subdomain_account->id;
        $subdomainaccount_kecamatan = $this->subdomain_account->kecamatanid;
        $data = array();

        $where = array(
            'a.id_user' => $subdomainaccount_id
        );

        $where_infografis = array(
            "(a.id_user = '$subdomainaccount_id' OR (c.id_kecamatan = '$subdomainaccount_kecamatan' AND b.admintype = 'Kecamatan')) =" => true
        );

        if ($this->subdomain_account->admintype == 'Kecamatan') {
            $infografis_pendidikan = "SELECT SUM(a.jumlah) AS jumlah, b.nama FROM msinfografis a JOIN kategori_infografis b ON b.id = a.id_kategori JOIN msusers c ON c.id = a.id_user LEFT JOIN kelurahan d ON d.id_kelurahan = c.kelurahanid WHERE b.type = 'Pendidikan' AND (a.id_user = '$subdomainaccount_id' OR d.id_kecamatan = '$subdomainaccount_kecamatan') GROUP BY b.nama";
            $infografis_pekerjaan = "SELECT SUM(a.jumlah) AS jumlah, b.nama FROM msinfografis a JOIN kategori_infografis b ON b.id = a.id_kategori JOIN msusers c ON c.id = a.id_user LEFT JOIN kelurahan d ON d.id_kelurahan = c.kelurahanid WHERE b.type = 'Pekerjaan' AND (a.id_user = '$subdomainaccount_id' OR d.id_kecamatan = '$subdomainaccount_kecamatan') GROUP BY b.nama";
        } else {
            $infografis_pendidikan = "SELECT a.jumlah, b.nama, b.icon FROM msinfografis a JOIN kategori_infografis b ON b.id = a.id_kategori WHERE b.type = 'Pendidikan' AND a.id_user = " . $this->subdomain_account->id;
            $infografis_pekerjaan = "SELECT a.jumlah, b.nama, b.icon FROM msinfografis a JOIN kategori_infografis b ON b.id = a.id_kategori WHERE b.type = 'Pekerjaan' AND a.id_user = " . $this->subdomain_account->id;
        }

        $setting = $this->settingumum->getDefaultData($where);
        $data['title'] = 'Infografis Desa';
        $data['setting'] = $setting->row();
        $data['infografis'] = $this->infografis->select('SUM(a.laki) AS laki, SUM(a.perempuan) AS perempuan')
            ->join('msusers b', 'b.id = a.id_user')
            ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid', 'LEFT')
            ->getDefaultData($where_infografis)
            ->row();
        $data['kontakpenting'] = $this->kontakpenting->getDefaultData(array('a.id_user' => $this->subdomain_account->id))->result();
        if ($this->subdomain_account->themeid == 2) {
            $data['infografis_pendidikan'] = $this->db->query($infografis_pendidikan)->result();
        } else {
            $data['infografis_pendidikan'] = array_chunk($this->db->query($infografis_pendidikan)->result(), 4);
        }
        $data['infografis_pekerjaan'] = $this->db->query($infografis_pekerjaan)->result();
        $data['subdomain_account'] = $this->subdomain_account;
        $data['subdomain_kecamatan'] = $subdomainaccount_kecamatan;

        if ($setting->num_rows() > 0) {
            // ProGides platform always uses ProGides theme regardless of theme setting
            if (getPlatformName() == 'ProGides') {
                $data['content'] = 'progides/infografis';
                return $this->load->view('progides/master', $data);
            } elseif ($this->subdomain_account->themeid == 2) {
                $data['content'] = 'profile/infografis';
                return $this->load->view('profile/master', $data);
            } else {
                $data['content'] = 'landing/infografis';
                return $this->load->view('landing/master', $data);
            }
        } else {
            return $this->load->view('profile_v2/configuration', $data);
        }
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = 'Infografis - Add';
        $data['content'] = 'infografis/add';
        $data['kategori'] = $this->kategori_info->getDefaultData(array(
            'a.id_user' => getCurrentIdUser(),
            'a.is_primary' => null
        ))->result();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $kategori = getPost('kategori');
        $jumlah = getPost('jumlah');

        $insert = array();
        $insert['id_user'] = getCurrentIdUser();
        $insert['id_kategori'] = $kategori;
        $insert['jumlah'] = $jumlah;

        $this->msinfografis->insert($insert);

        return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->msinfografis->get(array(
            'id' => $id,
            'id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $delete = $this->msinfografis->delete(array(
            'id' => $id
        ));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus data');
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return redirect('dashboard');
        }

        $get = $this->msinfografis->select('a.*, b.is_primary')
            ->join('kategori_infografis b', 'b.id = a.id_kategori')->getDefaultData(array(
                'a.id' => $id,
                'a.id_user' => getCurrentIdUser(),
            ));

        if ($get->num_rows() == 0) {
            return redirect('infografis');
        }

        $data = array();
        $data['title'] = 'Infografis - Edit';
        $data['content'] = 'infografis/edit';
        $data['infografis'] = $get->row();
        $data['kategori'] = $this->kategori_info->getDefaultData(array(
            'a.id_user' => getCurrentIdUser(),
            'a.is_primary' => null
        ))->result();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->msinfografis->get(array(
            'id' => $id,
            'id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $kategori = getPost('kategori');
        $jumlah = getPost('jumlah');

        $update = array();
        if ($kategori != null) {
            $update['id_kategori'] = $kategori;
        }
        $update['jumlah'] = $jumlah;

        $this->msinfografis->update(array('id' => $id), $update);

        return JSONResponseDefault('OK', 'Data berhasil diubah');
    }
}
