<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsPenandatangan $mspenandatangan
 * @property DetailKategori_Surat $detailkategorisurat
 * @property MsLetterAccess $msletteraccess
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver|CI_DB_query_builder $db
 */
class Penandatangan extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsPenandatangan', 'mspenandatangan');
        $this->load->model('DetailKategori_Surat', 'detailkategorisurat');
        $this->load->model('MsLetterAccess', 'msletteraccess');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Penandatangan';
        $data['content'] = 'penandatangan/index';

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Penandatangan';
        $data['content'] = 'penandatangan/add';
        $data['kategorisurat'] = $this->msletteraccess->select('b.nama, b.id')
            ->join('kategori_surat b', 'b.id = a.categoryid')
            ->order_by('a.id', 'DESC')
            ->result(array(
                'a.userid' => getCurrentIdUser()
            ));

        return $this->load->view('master', $data);
    }

    public function api_get()
    {
        $desaid = getPost('desaid');

        return JSONResponse(array(
            'RESULT' => 'OK',
            'PENANDATANGAN' => $this->detailkategorisurat->result(array('id_user' => $desaid))
        ));
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $nama = getPost('nama');
        $jabatan = getPost('jabatan');
        $phonenumber = getPost('phonenumber');
        $categoryid = getPost('categoryid');
        $address = getPost('address');

        if (empty($nama)) {
            return JSONResponseDefault('FAILED', 'Nama tidak boleh kosong!');
        } else if (empty($jabatan)) {
            return JSONResponseDefault('FAILED', 'Jabatan tidak boleh kosong!');
        } else if (empty($phonenumber)) {
            return JSONResponseDefault('FAILED', 'Nomor WhatsApp tidak boleh kosong!');
        } else if (empty($address)) {
            return JSONResponseDefault('FAILED', 'Alamat tidak boleh kosong!');
        }

        $data = array(
            'name' => $nama,
            'position' => $jabatan,
            'phonenumber' => $phonenumber,
            'userid' => getCurrentIdUser(),
            'address' => $address,
        );

        $this->mspenandatangan->insert($data);
        $penandatanganid = $this->db->insert_id();

        $exploding = explode(',', $categoryid);
        foreach ($exploding as $key => $value) {
            $detailkategorisurat = $this->detailkategorisurat->get(array(
                'catsuratid' => $value,
                'id_user' => getCurrentIdUser(),
                'LOWER(nama) =' => strtolower($nama),
                'LOWER(jabatan) =' => strtolower($jabatan),
            ));

            if ($detailkategorisurat->num_rows() == 0) {
                $insert_detail = array();
                $insert_detail['catsuratid'] = $value;
                $insert_detail['nama'] = $nama;
                $insert_detail['jabatan'] = $jabatan;
                $insert_detail['nomor_handphone'] = $phonenumber;
                $insert_detail['id_user'] = getCurrentIdUser();
                $insert_detail['penandatanganid'] = $penandatanganid;
                $insert_detail['address'] = $address;

                $this->detailkategorisurat->insert($insert_detail);
            } else {
                $update_detail = array();
                $update_detail['nomor_handphone'] = $phonenumber;
                $update_detail['penandatanganid'] = $penandatanganid;
                $update_detail['address'] = $address;

                $this->detailkategorisurat->update(array(
                    'catsuratid' => $value,
                    'id_user' => getCurrentIdUser()
                ), $update_detail);
            }
        }

        return JSONResponseDefault('OK', 'Penandatangan berhasil ditambahkan!');
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $datatables = $this->datatables->make('MsPenandatangan', 'QueryDatatables', 'SearchDatatables');

        $data = array();
        foreach ($datatables->getData(array('a.userid' => getCurrentIdUser())) as $key => $value) {
            $detail = $this->detailkategorisurat->select('b.nama')
                ->join('kategori_surat b', 'b.id = a.catsuratid')
                ->group_by('b.nama')
                ->result(array(
                    'a.penandatanganid' => $value->id,
                    'a.id_user' => getCurrentIdUser(),
                ));

            $ul = "<ul style=\"padding-left: 1.2rem;\">";

            foreach ($detail as $k => $v) {
                $ul .= "<li>$v->nama</li>";
            }

            $ul .= "</ul>";

            $detail = array();
            $detail[] = $value->name;
            $detail[] = $value->position;
            $detail[] = $ul;
            $detail[] = "<a href=\"https://wa.me/$value->phonenumber\" class=\"btn btn-success btn-sm\" target=\"_blank\">
                <i class=\"fab fa-whatsapp\"></i>
            </a>

            <a href=\"" . base_url('penandatangan/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm\">
                <i class=\"fa fa-edit\"></i>
            </a>

            <button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deletePenandatangan($value->id)\">
                <i class=\"fa fa-trash\"></i>
            </button>";

            $data[] = $detail;
        }

        return $datatables->json($data);
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $id = getPost('id');

        $get = $this->mspenandatangan->get(array(
            'a.id' => $id,
            'a.userid' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Penandatangan tidak ditemukan!');
        }

        $this->mspenandatangan->delete(array(
            'id' => $id,
        ));

        $this->detailkategorisurat->delete(array(
            'penandatanganid' => $id,
        ));

        return JSONResponseDefault('OK', 'Penandatangan berhasil dihapus!');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->mspenandatangan->get(array(
            'a.id' => $id,
            'a.userid' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('dashboard'));
        }

        $row = $get->row();

        $penandatangan = $this->detailkategorisurat->get(array(
            'a.id_user' => getCurrentIdUser(),
            'a.penandatanganid' => $id
        ))->result();

        $selectedcategory = array();
        foreach ($penandatangan as $key => $value) {
            $selectedcategory[] = $value->catsuratid;
        }

        $data = array();
        $data['title'] = 'Edit Penandatangan';
        $data['content'] = 'penandatangan/edit';
        $data['penandatangan'] = $get->row();
        $data['kategorisurat'] = $this->msletteraccess->select('b.nama, b.id')
            ->join('kategori_surat b', 'b.id = a.categoryid')
            ->order_by('a.id', 'DESC')
            ->result(array(
                'a.userid' => getCurrentIdUser()
            ));
        $data['selectedcategory'] = $selectedcategory;

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $get = $this->mspenandatangan->get(array(
            'a.id' => $id,
            'a.userid' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Penandatangan tidak ditemukan!');
        }

        $nama = getPost('nama');
        $jabatan = getPost('jabatan');
        $phonenumber = getPost('phonenumber');
        $categoryid = getPost('categoryid');
        $address = getPost('address');

        if (empty($nama)) {
            return JSONResponseDefault('FAILED', 'Nama tidak boleh kosong!');
        } else if (empty($jabatan)) {
            return JSONResponseDefault('FAILED', 'Jabatan tidak boleh kosong!');
        } else if (empty($phonenumber)) {
            return JSONResponseDefault('FAILED', 'Nomor WhatsApp tidak boleh kosong!');
        } else if (empty($address)) {
            return JSONResponseDefault('FAILED', 'Alamat tidak boleh kosong!');
        }

        $data = array(
            'name' => $nama,
            'position' => $jabatan,
            'phonenumber' => $phonenumber,
            'address' => $address,
        );

        $this->mspenandatangan->update(array(
            'id' => $id,
        ), $data);

        $exploding = explode(',', $categoryid);
        $excludesdelete = array();

        foreach ($exploding as $key => $value) {
            $get = $this->detailkategorisurat->get(array(
                'a.catsuratid' => $value,
                'a.id_user' => getCurrentIdUser(),
                'a.penandatanganid' => $id
            ));

            if ($get->num_rows() == 0) {
                $insert_detail = array();
                $insert_detail['catsuratid'] = $value;
                $insert_detail['nama'] = $nama;
                $insert_detail['jabatan'] = $jabatan;
                $insert_detail['nomor_handphone'] = $phonenumber;
                $insert_detail['penandatanganid'] = $id;
                $insert_detail['id_user'] = getCurrentIdUser();
                $insert_detail['address'] = $address;

                $this->detailkategorisurat->insert($insert_detail);
            } else {
                $update_detail = array();
                $update_detail['nomor_handphone'] = $phonenumber;
                $update_detail['penandatanganid'] = $id;
                $update_detail['address'] = $address;

                $this->detailkategorisurat->update(array(
                    'catsuratid' => $value,
                    'id_user' => getCurrentIdUser()
                ), $update_detail);
            }

            $excludesdelete[] = $value;
        }

        $this->detailkategorisurat->where_not_in(array(
            'catsuratid' => $excludesdelete,
        ))->delete(array(
            'penandatanganid' => $id,
            'id_user' => getCurrentIdUser(),
        ));

        return JSONResponseDefault('OK', 'Penandatangan berhasil diubah!');
    }
}
