<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Warga_Model $warga
 * @property Master_Users $masterusers
 */
class PMDController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Warga_Model', 'warga');
        $this->load->model('Master_Users', 'masterusers');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = 'Manage PMD';
        $data['content'] = 'managepmd/index';
        $data['pmd'] = $this->masterusers->select('a.*, b.nama_kabkota AS kabupaten')
            ->join('kabkota b', 'b.id_kabkota = a.kabkotaid')
            ->getDefaultData(array('role' => 'pmd'))->result();

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = 'Manage PMD - Add';
        $data['content'] = 'managepmd/add';
        $data['kabkota'] = $this->warga->getKabkota(array('id_propinsi' => 22))->result();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menambahkan PMD');
        }

        $username = getPost('username');
        $password = getPost('password');
        $kabupaten = getPost('kabupaten');
        $kecamatan = getPost('kecamatan');

        $users = $this->masterusers->getDefaultData(array(
            'username' => $username
        ));

        if ($users->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Username yang anda masukkan telah terdaftar');
        }

        $insert = array();
        $insert['username'] = $username;
        $insert['password'] = md5($password);
        $insert['role'] = 'pmd';
        $insert['kabkotaid'] = $kabupaten;
        $insert['kecamatanid'] = $kecamatan;

        $insert = $this->masterusers->insert($insert);

        if ($insert) {
            return JSONResponseDefault('OK', 'Berhasil menambahkan PMD');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menambahkan PMD');
        }
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menghapus PMD');
        }

        $id = getPost('id');

        $delete = $this->masterusers->delete(array('id' => $id));

        if ($delete) {
            return JSONResponseDefault('OK', 'Berhasil menghapus PMD');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus PMD');
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin()) {
            return redirect('dashboard');
        }

        $get = $this->masterusers->getDefaultData(array(
            'a.id' => $id
        ));

        if ($get->num_rows() == 0) {
            return redirect('manage/admin');
        }

        $data = array();
        $data['title'] = 'Manage PMD - Edit';
        $data['content'] = 'managepmd/edit';
        $data['pmd'] = $get->row();
        $data['kabkota'] = $this->warga->getKabkota(array('id_propinsi' => 22))->result();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk mengubah PMD');
        }

        $username = getPost('username');
        $password = getPost('password');
        $kabupaten = getPost('kabupaten');
        $kecamatan = getPost('kecamatan');

        $pmd = $this->masterusers->getDefaultData(array('id' => $id));

        if ($pmd->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'PMD tidak ditemukan');
        }

        $row = $pmd->row();

        if ($row->username != $username) {
            $pmds = $this->masterusers->getDefaultData(array('username' => $username));

            if ($pmds->num_rows() > 0) {
                return JSONResponseDefault('FAILED', 'Username yang anda masukkan telah terdaftar');
            }
        }

        $update = array();
        $update['username'] = $username;
        $update['kabkotaid'] = $kabupaten;
        $update['kecamatanid'] = $kecamatan;

        if ($password != null) {
            $update['password'] = md5($password);
        }

        $update = $this->masterusers->update(array('id' => $id), $update);

        if ($update) {
            return JSONResponseDefault('OK', 'Berhasil mengubah PMD');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal mengubah PMD');
        }
    }
}
