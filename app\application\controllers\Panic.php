<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property FirebaseToken $firebasetoken
 * @property CI_Output $output
 * @property CI_DB_mysqli_driver $db
 */
class Panic extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('FirebaseToken', 'firebasetoken');
    }

    private function trigger_notification($token, $lat, $lng, $type)
    {
        $data = json_encode(array(
            'token' => $token,
            'lat' => $lat,
            'lng' => $lng,
            'type' => $type
        ));

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://gidesopenai-tdhcyhtcbq-uc.a.run.app/send/panic',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);

        return json_decode($response);
    }

    public function api_send()
    {
        $type = getPost('type');
        $lat = getPost('lat');
        $lng = getPost('lng');

        $border = $this->db->query("SELECT a.username, b.nama_kelurahan, c.nama_kecamatan, d.nama_kabkota, e.border FROM msusers a LEFT JOIN kelurahan b ON b.id_kelurahan = a.kelurahanid LEFT JOIN kecamatan c ON c.id_kecamatan = b.id_kecamatan LEFT JOIN kabkota d ON d.id_kabkota = c.id_kabkota LEFT JOIN villagesborder e ON e.village = b.nama_kelurahan AND e.sub_district = c.nama_kecamatan WHERE a.id = '" . $this->subdomain_account->id . "' ORDER BY c.nama_kecamatan asc")->row();

        if ($border->border != null) {
            $border_decoded = json_decode($border->border);

            /* $isWithinBoundary = validateCoordinate($lat, $lng, $border_decoded);

            if (!$isWithinBoundary) {
                $this->output->set_status_header(400);
                $this->output->set_content_type('application/json');

                return JSONResponse(array(
                    'status' => 'error',
                    'message' => 'Lokasi anda diluar desa'
                ));
            } */
        } else {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => 'error',
                'message' => 'Desa anda tidak mendukung fitur ini'
            ));
        }

        $token = $this->firebasetoken->result(array(
            'userid' => $this->subdomain_account->id,
        ));

        foreach ($token as $key => $value) {
            $this->trigger_notification($value->token, $lat, $lng, $type);
        }

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => 'success',
            'message' => 'Panic button has been triggered'
        ));
    }
}
