<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @class ManageAdminController
 * @property Master_Users $masterusers
 * @property Berita_Model $berita
 * @property Infografis_Model $infografis
 * @property Kontak_Penting $kontakpenting
 * @property Pemerintahan_Model $pemerintahan
 * @property Produk_Model $produk
 * @property Slider_model $slider
 * @property Setting_Umum $settingumum
 * @property Wisata_Model $wisata
 * @property APBDesa_Model $apbdesa
 * @property APBDetailDesa $apbdetaildesa
 * @property Kategori_InfografisModel $kategori_infografis
 * @property Master_Infografis $msinfografis
 * @property Warga_Model $warga
 * @property Kategori_Surat $kategorisurat
 * @property MsLetterAccess $msletteraccess
 * @property HistoryRoyalty $historyroyalty
 * @property CI_DB_query_builder|CI_DB_mysqli_driver $db
 * @property BPD_Notification $bpdnotification
 * @property Model_Surat $modelsurat
 * @property Model_Surat_Access $modelsurataccess
 * @property Request_Domain $request_domain
 * @property Kecamatan $kecamatan
 */
class ManageAdminController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Master_Users', 'masterusers');
        $this->load->model('Berita_Model', 'berita');
        $this->load->model('Infografis_Model', 'infografis');
        $this->load->model('Kontak_Penting', 'kontakpenting');
        $this->load->model('Pemerintahan_Model', 'pemerintahan');
        $this->load->model('Produk_Model', 'produk');
        $this->load->model('Slider_model', 'slider');
        $this->load->model('Setting_Umum', 'settingumum');
        $this->load->model('Wisata_Model', 'wisata');
        $this->load->model('APBDesa_Model', 'apbdesa');
        $this->load->model('APBDetailDesa', 'apbdetaildesa');
        $this->load->model('Kategori_InfografisModel', 'kategori_infografis');
        $this->load->model('Master_Infografis', 'msinfografis');
        $this->load->model('Warga_Model', 'warga');
        $this->load->model('Kategori_Surat', 'kategorisurat');
        $this->load->model('MsLetterAccess', 'msletteraccess');
        $this->load->model('HistoryRoyalty', 'historyroyalty');
        $this->load->model('BPD_Notification', 'bpdnotification');
        $this->load->model('Model_Surat', 'modelsurat');
        $this->load->model('Model_Surat_Access', 'modelsurataccess');
        $this->load->model('Request_Domain', 'request_domain');
        $this->load->model('Kecamatan', 'kecamatan');
    }

    public function index()
    {
        $kabupaten = getGet('kabupaten');
        $kecamatan = getGet('kecamatan');

        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin() && !isDiskominfo()) {
            return redirect('dashboard');
        }

        $where = array(
            "(a.role = 'admin' OR (a.role = 'kecamatan' AND a.admintype = 'Kecamatan' AND a.username IS NOT NULL)) =" => true,
        );

        if ($kabupaten != null) {
            $where['d.id_kabkota'] = $kabupaten;
        }

        if ($kecamatan != null) {
            $where['c.id_kecamatan'] = $kecamatan;
        }

        if (!isAllPlatform()) {
            $where['a.platformname'] = getCurrentPlatformName();
        }

        $where_kabupaten = array();
        if (!isAllPlatform()) {
            $where_kabupaten['a.platformname'] = getCurrentPlatformName();
        }

        $data = array();
        $data['title'] = 'Manage Admin';
        $data['content'] = 'manageadmin/index';
        $data['admin'] = $this->masterusers->select('a.*')
            ->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid', 'LEFT')
            ->join('kecamatan c', 'c.id_kecamatan = b.id_kecamatan OR c.id_kecamatan = a.kecamatanid', 'LEFT')
            ->join('kabkota d', 'd.id_kabkota = c.id_kabkota', 'LEFT')
            ->getDefaultData($where);
        $data['kabupaten'] = $this->masterusers->select('d.id_kabkota, d.nama_kabkota')
            ->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
            ->join('kecamatan c', 'c.id_kecamatan = b.id_kecamatan')
            ->join('kabkota d', 'd.id_kabkota = c.id_kabkota')
            ->group_by('d.id_kabkota, d.nama_kabkota')
            ->result($where_kabupaten);

        $data['selectedkabupaten'] = $kabupaten;
        $data['selectedkecamatan'] = $kecamatan;

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = 'Manage Admin - Add';
        $data['content'] = 'manageadmin/add';
        $data['provinsi'] = $this->warga->getProvinsi()->result();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isPostAjax()) {
            return JSONResponseRequestReject();
        }

        $username = getPost('username');
        $password = getPost('password');
        $subdomain = getPost('subdomain');
        $kecamatan = getPost('kecamatan');
        $kelurahan = getPost('kelurahan');
        $admintype = getPost('admintype');

        if (isAllPlatform()) {
            $platform = getPost('platform');
        } else {
            $platform = getCurrentPlatformName();
        }

        if ($platform == 'Gides') {
            $getUsername = $this->masterusers->getDefaultData(array(
                'a.username' => $username,
                "(a.platformname = '$platform' OR a.platformname IS NULL) =" => true
            ));
        } else {
            $getUsername = $this->masterusers->getDefaultData(array(
                'a.username' => $username,
                'a.platformname' => $platform
            ));
        }

        if ($getUsername->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Username yang anda masukkan telah terdaftar');
        }

        if ($platform == 'Gides') {
            $getSubdomain = $this->masterusers->getDefaultData(array(
                'a.subdomain' => $subdomain,
                "(a.platformname = '$platform' OR a.platformname IS NULL) =" => true
            ));
        } else {
            $getSubdomain = $this->masterusers->getDefaultData(array(
                'a.subdomain' => $subdomain,
                'a.platformname' => $platform
            ));
        }

        if ($getSubdomain->num_rows()  > 0) {
            return JSONResponseDefault('FAILED', 'Subdomain yang anda masukkan telah digunakan');
        }

        if ($admintype != 'Kecamatan') {
            $get_duplicate_kelurahan = $this->masterusers->getDefaultData(array(
                'a.kelurahanid' => $kelurahan
            ));

            if ($get_duplicate_kelurahan->num_rows() > 0) {
                return JSONResponseDefault('FAILED', 'Kelurahan yang anda pilih telah memiliki admin');
            }
        }

        $subdistrict_exists = false;
        if ($admintype == 'Kecamatan') {
            $subdistrict_account = $this->masterusers->get(array(
                'kecamatanid' => $kecamatan,
                'role' => 'kecamatan'
            ));

            if ($subdistrict_account->num_rows() > 0) {
                $subdistrict_exists = true;
            }
        }

        if ($admintype != 'Kecamatan') {
            $insert = array();
            $insert['username'] = $username;
            $insert['password'] = md5($password);
            $insert['subdomain'] = $subdomain;
            $insert['role'] = 'admin';
            $insert['kelurahanid'] = $kelurahan;
            $insert['admintype'] = $admintype;
            $insert['platformname'] = $platform;

            $insert = $this->masterusers->insert($insert);
            $id_user = $this->db->insert_id();
        } else {
            if ($subdistrict_exists) {
                $update = array();
                $update['subdomain'] = $subdomain;
                $update['admintype'] = $admintype;
                $update['platformname'] = $platform;

                $this->masterusers->update(array(
                    'kecamatanid' => $kecamatan,
                    'role' => 'kecamatan'
                ), $update);

                $id_user = $subdistrict_account->row()->id;
            } else {
                $get_district = $this->kecamatan->get(array(
                    'id_kecamatan' => $kecamatan
                ))->row();

                $insert = array();
                $insert['username'] = $username;
                $insert['password'] = md5($password);
                $insert['subdomain'] = $subdomain;
                $insert['role'] = 'kecamatan';
                $insert['kabkotaid'] = $get_district->id_kabkota;
                $insert['kecamatanid'] = $kecamatan;
                $insert['admintype'] = $admintype;
                $insert['platformname'] = $platform;

                $insert = $this->masterusers->insert($insert);
                $id_user = $this->db->insert_id();
            }
        }

        $kategori_infografis = $this->kategori_infografis->getDefaultData(array(
            'a.is_primary' => 1
        ));

        foreach ($kategori_infografis->result() as $key => $value) {
            $get_row = $this->msinfografis->get(array(
                'id_user' => $id_user,
                'id_kategori' => $value->id
            ));

            if ($get_row->num_rows() == 0) {
                $insert_infografis = array();
                $insert_infografis['id_user'] = $id_user;
                $insert_infografis['id_kategori'] = $value->id;
                $insert_infografis['jumlah'] = 0;

                $this->msinfografis->insert($insert_infografis);
            }
        }

        $insert = array();
        if ($admintype == 'Desa') {
            $insert['visi'] = "<ol><li>Membangun desa yang modern, mandiri, dan sejahtera bagi seluruh warga.</li><li>Menjadikan desa sebagai sentra perekonmian dan pendidikan unggul bagi masyarakat setempat.</li><li>Menciptakan lingkungan hidup bersih, sehat, dan asri bagi warga desa.</li><li>Menghadirkan fasilitas dan pelayanan publik yang berkualitas untuk kemajuan desa.</li><li>Menumbuhkan budaya gotong royong, toleransi, dan kerja sama bagi kemajuan desa.</li></ol>";
            $insert['misi'] = "<ol><li>Menyediakan fasilitas dan pelayanan publik yang berkualitas bagi warga desa.</li><li>Menciptakan lingkungan hidup bersih, sehat, dan asri bagi warga desa</li><li>Menumbuhkan budaya gotong royong, toleransi, dan kerja sama bagi kemajuan desa.</li><li>Menjadikan desa sebagai sentra perekonimian dan pendidikan unggul bagi masyarakat setempat.</li><li>Menciptakan lapangan pekerjaan bagi warga desa untuk meningkatkan kesejahteraan ekonomi mereka.</li><li>Menyediakan akses air bersih dan listrik bagi seluruh warga desa.</li><li>Mewujudkan sistem pemerintahan yang transparaan, akuntabel, dan bertanggung jawab.</li></ol>";
        } else if ($admintype == 'Kelurahan') {
            $insert['visi'] = "<ol><li>Menciptakan kelurahan yang maju, adil, dan sejahtera bagi seluruh warganya</li><li>Menjadikan kelurahan sebagai sentra perekonomian dan pendidikan unggul bagi masyarakat setempat</li><li>Menghadirkan lingkungan hidup bersih, sehat, dan asri bagi warga kelurahan</li><li>Menyediakan fasilitas dan pelayanan publik yang berkualitas untuk kemajuan kelurahan</li><li>Menumbuhkan budaya gotong royong, toleransi, dan kerja sama bagi kemajuan kelurahan</li></ol>";
            $insert['misi'] = "<ol><li>Menyediakan fasilitas dan pelayanan publik yang berkualitas bagi warga kelurahan</li><li>Menciptakan lingkungan hidup bersih, sehat, dan asri bagi warga kelurahan</li><li>Menumbuhkan budaya gotong royong, toleransi dan pendidikan unggul bagi masyarakat setempat</li><li>Menciptakan kelurahan sebagai sentra perekonomian dan pendidikan unggul bagi masyarakat setempat</li><li>Menciptakan lapangan pekerjaan bagi warga kelurahan untuk meningkatkan kesejahteraan ekonomi mereka</li><li>Menyediakan akses air bersih dan listrik bagi seluruh warga kelurahan</li><li>Mewujudkan sistem pemerintahan yang transparan, akuntabel, dan bertanggung jawab</li></ol>";
        } else if ($admintype == 'Kecamatan') {
            $insert['visi'] = "<ol><li>Menciptakan kecamatan yang maju, adil, dan sejahtera bagi seluruh warganya</li><li>Menjadikan kecamatan sebagai sentra perekonomian dan pendidikan unggul bagi masyarakat setempat</li><li>Menghadirkan lingkungan hidup bersih, sehat, dan asri bagi warga kecamatan</li><li>Menyediakan fasilitas dan pelayanan publik yang berkualitas untuk kemajuan kecamatan</li><li>Menumbuhkan budaya gotong royong, toleransi, dan kerja sama bagi kemajuan kecamatan</li></ol>";
            $insert['misi'] = "<ol><li>Menyediakan fasilitas dan pelayanan publik yang berkualitas bagi warga kecamatan</li><li>Menciptakan lingkungan hidup bersih, sehat, dan asri bagi warga kecamatan</li><li>Menumbuhkan budaya gotong royong, toleransi, dan kerja sama bagi kemajuan kecamatan</li><li>Menjadikan kecamatan sebagai sentra perekonomian dan pendidikan unggul bagi masyarakat setempat</li><li>Menciptakan lapangan pekerjaan bagi warga kecamatan untuk meningkatkan kesejahteraan ekonomi mereka</li><li>Menyediakan akses air bersih dan listrik bagi seluruh warga kecamatan</li><li>Mewujudkan sistem pemerintahan yang transparan, akuntabel, dan bertanggung jawab</li></ol>";
        }
        $insert['id_user'] = $id_user;
        $this->pemerintahan->insert($insert);

        $insert = array();
        if ($admintype == "Desa") {
            $insert['isi'] = "<p>Selamat datang di website resmi Desa kami.</p><p>Sebagai Kepala Desa, saya sangat bangga dan senang dapat menyambut Anda di sini, Desa Kami merupakan desa yang dikenal dengan keramahan dan kebersamaan warga, Kami percaya bahwa dengan bekerja sama, kita dapat menciptakan desa yang lebih baik dan sejahtera bagi generasi masa depan.</p><p>Dengan adanya website ini, kami berharap dapat memberikan informasi yang berguna dan akurat bagi masyarakat, serta mempermudah akses bagi masyarakat untuk berinteraksi dan berkoordinasi dengan pemerintah desa.</p><p>Terima kasih atas kunjungan Anda&nbsp; dan semoga website ini dapat memberikan manfaat bagi Anda semua.</p><p>Salam hangat</p>";
        } else if ($admintype == "Kelurahan") {
            $insert['isi'] = "<p>Selamat datang di website resmi Kelurahan kami.</p><p>Sebagai Lurah, saya sangat bangga dan senang dapat menyambut Anda di sini, Kelurahan Kami merupakan kelurahan yang dikenal dengan keramahan dan kebersamaan warga, Kami percaya bahwa dengan bekerja sama, kita dapat menciptakan kelurahan yang lebih baik dan sejahtera bagi generasi masa depan.</p><p>Dengan adanya website ini, kami berharap dapat memberikan informasi yang berguna dan akurat bagi masyarakat, serta mempermudah akses bagi masyarakat untuk berinteraksi dan berkoordinasi dengan pemerintah kelurahan.</p><p>Terima kasih atas kunjungan Anda&nbsp; dan semoga website ini dapat memberikan manfaat bagi Anda semua.</p><p>Salam hangat</p>";
        } else if ($admintype == 'Kecamatan') {
            $insert['isi'] = "<p>Selamat datang di website resmi Kecamatan kami.</p><p>Sebagai Camat, saya sangat bangga dan senang dapat menyambut Anda di sini, Kecamatan Kami merupakan kecamatan yang dikenal dengan keramahan dan kebersamaan warga, Kami percaya bahwa dengan bekerja sama, kita dapat menciptakan kecamatan yang lebih baik dan sejahtera bagi generasi masa depan.</p><p>Dengan adanya website ini, kami berharap dapat memberikan informasi yang berguna dan akurat bagi masyarakat, serta mempermudah akses bagi masyarakat untuk berinteraksi dan berkoordinasi dengan pemerintah kecamatan.</p><p>Terima kasih atas kunjungan Anda&nbsp; dan semoga website ini dapat memberikan manfaat bagi Anda semua.</p><p>Salam hangat</p>";
        }
        $insert['id_user'] = $id_user;
        $this->settingumum->insert($insert);

        if ($insert) {
            return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menambahkan data');
        }
    }

    public function process_delete()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $id = getPost('id');

        $delete = $this->masterusers->delete(array(
            'id' => $id
        ));

        $this->berita->delete(array(
            'id_user' => $id
        ));

        $this->infografis->delete(array(
            'id_user' => $id
        ));

        $this->kontakpenting->delete(array(
            'id_user' => $id
        ));

        $this->pemerintahan->delete(array(
            'id_user' => $id
        ));

        $this->produk->delete(array(
            'id_user' => $id
        ));

        $this->slider->delete(array(
            'id_user' => $id
        ));

        $this->settingumum->delete(array(
            'id_user' => $id
        ));

        $this->wisata->delete(array(
            'id_user' => $id
        ));

        $get_apb = $this->apbdesa->getDefaultData(array(
            'id_user' => $id
        ));

        if ($get_apb->num_rows() > 0) {
            $row = $get_apb->row();

            $this->apbdesa->delete(array(
                'id_user' => $id
            ));

            $this->apbdetaildesa->delete(array(
                'headerid' => $row->id
            ));
        }

        if ($delete) {
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus data');
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin()) {
            return redirect('dashboard');
        }

        $get = $this->masterusers->select('a.*, CASE WHEN b.id_kabkota IS NULL THEN a.kabkotaid ELSE b.id_kabkota END AS id_kabkota, CASE WHEN b.id_kecamatan IS NULL THEN a.kecamatanid ELSE b.id_kecamatan END AS id_kecamatan, c.id_propinsi', false)
            ->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid', 'LEFT')
            ->join('kabkota c', 'c.id_kabkota = b.id_kabkota OR c.id_kabkota = a.kabkotaid', 'LEFT')
            ->getDefaultData(array(
                'a.id' => $id
            ));

        if ($get->num_rows() == 0) {
            return redirect('manage/admin');
        }

        $data = array();
        $data['title'] = 'Manage Admin - Edit';
        $data['content'] = 'manageadmin/edit';
        $data['admin'] = $get->row();
        $data['provinsi'] = $this->warga->getProvinsi()->result();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $get = $this->masterusers->getDefaultData(array(
            'a.id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        $username = getPost('username');
        $password = getPost('password');
        $subdomain = getPost('subdomain');
        $kecamatan = getPost('kecamatan');
        $kelurahan = getPost('kelurahan');
        $admintype = getPost('admintype');

        if (isAllPlatform()) {
            $platform = getPost('platform');
        } else {
            $platform = getCurrentPlatformName();
        }

        if ($username != $row->username) {
            if ($platform == 'Gides') {
                $getUsername = $this->masterusers->getDefaultData(array(
                    'a.username' => $username,
                    "(a.platformname = '$platform' OR a.platformname IS NULL) =" => true
                ));
            } else {
                $getUsername = $this->masterusers->getDefaultData(array(
                    'a.username' => $username,
                    'a.platformname' => $platform
                ));
            }

            if ($getUsername->num_rows() > 0) {
                return JSONResponseDefault('FAILED', 'Username yang anda masukkan telah terdaftar');
            }
        }

        if ($subdomain != $row->subdomain) {
            if ($platform == 'Gides') {
                $getSubdomain = $this->masterusers->getDefaultData(array(
                    'a.subdomain' => $subdomain,
                    "(a.platformname = '$platform' OR a.platformname IS NULL) =" => true
                ));
            } else {
                $getSubdomain = $this->masterusers->getDefaultData(array(
                    'a.subdomain' => $subdomain,
                    'a.platformname' => $platform
                ));
            }

            if ($getSubdomain->num_rows()  > 0) {
                return JSONResponseDefault('FAILED', 'Subdomain yang anda masukkan telah digunakan');
            }
        }

        if ($admintype != 'Kecamatan') {
            if ($row->kelurahanid != $kelurahan) {
                $get_duplicate_kelurahan = $this->masterusers->getDefaultData(array(
                    'a.kelurahanid' => $kelurahan
                ));

                if ($get_duplicate_kelurahan->num_rows() > 0) {
                    return JSONResponseDefault('FAILED', 'Kelurahan yang anda pilih telah digunakan');
                }

                $warga = $this->warga->result(array(
                    'id_kelurahan' => $row->kelurahanid
                ));

                foreach ($warga as $key => $value) {
                    $this->warga->update(array(
                        'nik' => $value->nik
                    ), array(
                        'id_kelurahan' => $kelurahan
                    ));
                }
            }
        }

        $subdistricts_exists = true;
        if ($admintype == 'Kecamatan' && $kecamatan != $row->kecamatanid) {
            $subdistrict_account = $this->masterusers->get(array(
                'kecamatanid' => $kecamatan,
                'role' => 'kecamatan',
                'subdomain !=' => null,
                'id !=' => $id
            ));

            if ($subdistrict_account->num_rows() > 0) {
                return JSONResponseDefault('FAILED', 'Kecamatan yang anda pilih telah memiliki admin');
            }

            $validate_username = $this->masterusers->get(array(
                'username' => $username,
                'role' => 'kecamatan',
            ));

            if ($validate_username->num_rows() > 0) {
                return JSONResponseDefault('FAILED', 'Username yang anda masukkan telah terdaftar');
            }

            $subdistricts_exists = false;
        }

        if ($subdistricts_exists) {
            $update = array();
            $update['username'] = $username;
            $update['subdomain'] = $subdomain;

            if ($admintype != 'Kecamatan') {
                $update['kelurahanid'] = $kelurahan;
            }

            $update['admintype'] = $admintype;
            $update['platformname'] = $platform;
            $update['kecamatanid'] = $kecamatan;

            if ($password != null && $admintype != 'Kecamatan') {
                $update['password'] = md5($password);
            }

            $update = $this->masterusers->update(array(
                'id' => $id
            ), $update);
        } else {
            $validate_subdomain = $this->masterusers->get(array(
                'subdomain' => $subdomain,
            ));

            if ($validate_subdomain->num_rows() > 0) {
                return JSONResponseDefault('FAILED', 'Subdomain yang anda masukkan telah digunakan');
            }

            $validate_username = $this->masterusers->get(array(
                'username' => $username,
            ));

            if ($validate_username->num_rows() > 0) {
                return JSONResponseDefault('FAILED', 'Username yang anda masukkan telah terdaftar');
            }

            if ($password == null) {
                return JSONResponseDefault('FAILED', 'Password tidak boleh kosong');
            }

            $get_kecamatan = $this->kecamatan->get(array(
                'id_kecamatan' => $kecamatan
            ))->row();

            $insert = array();
            $insert['subdomain'] = $subdomain;
            $insert['username'] = $username;
            $insert['password'] = md5($password);
            $insert['role'] = 'kecamatan';
            $insert['createddate'] = getCurrentDate();
            $insert['kabkotaid'] = $get_kecamatan->id_kabkota;
            $insert['kecamatanid'] = $kecamatan;
            $insert['admintype'] = $admintype;
            $insert['platformname'] = $platform;

            $this->masterusers->insert($insert);
            $id = $this->db->insert_id();
        }

        $kategori_infografis = $this->kategori_infografis->getDefaultData(array(
            'a.is_primary' => 1
        ));

        foreach ($kategori_infografis->result() as $key => $value) {
            $infografis_new = $this->msinfografis->getDefaultData(array('a.id_kategori' => $value->id, 'a.id_user' => $id));

            if ($infografis_new->num_rows() == 0) {
                $insert_infografis = array();
                $insert_infografis['id_user'] = $id;
                $insert_infografis['id_kategori'] = $value->id;
                $insert_infografis['jumlah'] = 0;

                $this->msinfografis->insert($insert_infografis);
            }
        }

        if ($update) {
            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal mengubah data');
        }
    }

    public function royalty($id)
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin()) {
            return redirect('dashboard');
        }

        $get = $this->masterusers->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/admin'));
        }

        $data = array();
        $data['title'] = 'Royalty';
        $data['content'] = 'manageadmin/royalty';
        $data['user'] = $get->row();
        $data['history'] = $this->historyroyalty->result(array('userid' => $id));

        return $this->load->view('master', $data);
    }

    public function process_royalty($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $get = $this->masterusers->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        $amount = getPost('amount');
        $type = getPost('type');

        if ($amount == null || $type == null) {
            return JSONResponseDefault('FAILED', 'Data tidak lengkap');
        }

        if ($type == 'Masuk') {
            $amounts = $row->royalty + $amount;
        } else {
            $amounts = $row->royalty - $amount;
        }

        $update = $this->masterusers->update(array(
            'id' => $id
        ), array(
            'royalty' => $amounts
        ));

        $insert = array();
        $insert['userid'] = $id;
        $insert['amount'] = $amount;
        $insert['type'] = $type;
        $insert['createddate'] = getCurrentDate();

        $this->historyroyalty->insert($insert);

        if ($update) {
            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal mengubah data');
        }
    }

    public function set_status()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isSuperAdmin() && !isDiskominfo()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $id = getPost('id');
        $status = getPost('status');

        $get = $this->masterusers->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $update = $this->masterusers->update(array(
            'id' => $id
        ), array(
            'isdisabled' => $status == 1 ? 0 : 1
        ));

        if ($update) {
            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal mengubah data');
        }
    }

    public function bpd($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->masterusers->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/admin'));
        }

        $row = $get->row();

        $bpd = $this->masterusers->get(array(
            'userid' => $row->id
        ))->row();

        $data = array();
        $data['title'] = 'Akses BPD - ' . $row->username;
        $data['content'] = 'manageadmin/bpd';
        $data['user'] = $row;
        $data['bpd'] = $bpd;
        $data['notification'] = $this->bpdnotification->result(array(
            'userid' => $id
        ));

        return $this->load->view('master', $data);
    }

    public function process_bpd($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $get = $this->masterusers->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        $username = getPost('username');
        $password = getPost('password');

        if ($username == null) {
            return JSONResponseDefault('FAILED', 'Username tidak boleh kosong');
        }

        $check_bpd = $this->masterusers->get(array(
            'userid' => $row->id
        ));

        if ($check_bpd->num_rows() == 0) {
            $check = $this->masterusers->get(array(
                'username' => $username
            ));

            if ($check->num_rows() > 0) {
                return JSONResponseDefault('FAILED', 'Username sudah digunakan');
            }

            if ($password == null) {
                return JSONResponseDefault('FAILED', 'Password tidak boleh kosong');
            }
        } else {
            if ($check_bpd->row()->username != $username) {
                $check = $this->masterusers->get(array(
                    'username' => $username
                ));

                if ($check->num_rows() > 0) {
                    return JSONResponseDefault('FAILED', 'Username sudah digunakan');
                }
            }
        }

        $execute = array();
        $execute['username'] = $username;

        if ($password != null) {
            $execute['password'] = md5($password);
        }

        $execute['role'] = 'bpd';

        if ($check_bpd->num_rows() == 0) {
            $execute['createddate'] = getCurrentDate();
            $execute['createdby'] = getCurrentIdUser();
        } else {
            $execute['updateddate'] = getCurrentDate();
            $execute['updatedby'] = getCurrentIdUser();
        }

        $execute['userid'] = $row->id;

        if ($check_bpd->num_rows() == 0) {
            $this->masterusers->insert($execute);
        } else {
            $this->masterusers->update(array(
                'userid' => $row->id
            ), $execute);
        }

        return JSONResponseDefault('OK', 'Data berhasil diubah');
    }

    public function add_bpd($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $get = $this->masterusers->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('manageadmin/notification_bpd/add', array(
                'user' => $row
            ), true)
        ));
    }

    public function process_add_bpd($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $get = $this->masterusers->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $name = getPost('name');
        $phonenumber = getPost('phonenumber');

        $insert = array();
        $insert['userid'] = $id;
        $insert['name'] = $name;
        $insert['phonenumber'] = $phonenumber;
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $this->bpdnotification->insert($insert);

        return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
    }

    public function process_delete_bpd($userid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $id = getPost('id');

        $get = $this->masterusers->get(array(
            'id' => $userid
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $this->bpdnotification->delete(array(
            'userid' => $userid,
            'id' => $id
        ));

        return JSONResponseDefault('OK', 'Data berhasil dihapus');
    }

    public function edit_bpd($userid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $id = getPost('id');

        $get = $this->masterusers->get(array(
            'id' => $userid
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $get_notification = $this->bpdnotification->get(array(
            'userid' => $userid,
            'id' => $id
        ));

        if ($get_notification->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get_notification->row();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('manageadmin/notification_bpd/edit', array(
                'user' => $get->row(),
                'notification' => $row
            ), true)
        ));
    }

    public function process_edit_bpd($userid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $id = getPost('id');

        $get = $this->masterusers->get(array(
            'id' => $userid
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $get_notification = $this->bpdnotification->get(array(
            'userid' => $userid,
            'id' => $id
        ));

        if ($get_notification->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $name = getPost('name');
        $phonenumber = getPost('phonenumber');

        $update = array();
        $update['name'] = $name;
        $update['phonenumber'] = $phonenumber;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->bpdnotification->update(array(
            'userid' => $userid,
            'id' => $id
        ), $update);

        return JSONResponseDefault('OK', 'Data berhasil diubah');
    }

    public function surat($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->masterusers->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('dashboard'));
        }

        $row = $get->row();

        $accessid = array();
        $letteraccess = $this->msletteraccess->select('a.*, b.nama')
            ->join('kategori_surat b', 'b.id = a.categoryid')
            ->result(array(
                'a.userid' => $id
            ));

        foreach ($letteraccess as $key => $value) {
            $accessid[] = $value->categoryid;
        }

        $data = array();
        $data['title'] = 'Akses Surat';
        $data['content'] = 'manageadmin/surat';
        $data['user'] = $row;
        $data['kategorisurat'] = $this->kategorisurat->order_by('a.nama', 'ASC')->result();
        $data['access'] = $accessid;
        $data['letteraccess'] = $letteraccess;

        return $this->load->view('master', $data);
    }

    public function process_surat($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $get = $this->masterusers->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $get_letteraccess = $this->msletteraccess->result(array(
            'userid' => $id
        ));

        $oldletter = array();
        foreach ($get_letteraccess as $key => $value) {
            $oldletter[] = $value->categoryid;
        }

        $access = getPost('categoryid', []);

        $insert = array();
        $delete = array();

        foreach ($access as $key => $value) {
            if (!in_array($value, $oldletter)) {
                $insert[] = array(
                    'userid' => $id,
                    'categoryid' => $value,
                    'createddate' => getCurrentDate(),
                    'createdby' => getCurrentIdUser()
                );
            }
        }

        foreach ($oldletter as $key => $value) {
            if (!in_array($value, $access)) {
                $delete[] = $value;
            }
        }

        if (count($insert) > 0) {
            $this->msletteraccess->insert_batch($insert);
        }

        foreach ($delete as $key => $value) {
            $this->msletteraccess->delete(array(
                'userid' => $id,
                'categoryid' => $value
            ));
        }

        return JSONResponseDefault('OK', 'Data berhasil disimpan');
    }

    public function model_surat($userid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $id = getPost('id');

        $get = $this->masterusers->get(array(
            'id' => $userid
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $get_letteraccess = $this->msletteraccess->select('a.*, b.nama')
            ->join('kategori_surat b', 'b.id = a.categoryid')
            ->get(array(
                'a.userid' => $userid,
                'a.id' => $id
            ));

        if ($get_letteraccess->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get_letteraccess->row();

        $model = $this->modelsurat->result(array(
            'categorylettersid' => $row->categoryid
        ));

        $model_access = $this->modelsurataccess->select('a.*, b.modelname')
            ->join('model_surat b', 'b.id = a.modelid')
            ->result(array(
                'a.userid' => $userid,
                'b.categorylettersid' => $row->categoryid
            ));

        $modelaccessid = array();
        foreach ($model_access as $key => $value) {
            $modelaccessid[] = $value->modelid;
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('manageadmin/model/edit', array(
                'user' => $get->row(),
                'letteraccess' => $row,
                'model' => $model,
                'modelaccess' => $modelaccessid
            ), true)
        ));
    }

    public function process_model_surat($userid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $modelid = getPost('modelid', []);
        $categorylettersid = getPost('categorylettersid');

        $get = $this->masterusers->get(array(
            'id' => $userid
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $model = $this->modelsurat->where_in('id', $modelid)->result();

        if (count($model) == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $get_model_access = $this->modelsurataccess->select('a.*')
            ->join('model_surat b', 'b.id = a.modelid')
            ->result(array(
                'a.userid' => $userid,
                'b.categorylettersid' => $categorylettersid,
            ));

        $oldmodel = array();
        foreach ($get_model_access as $key => $value) {
            $oldmodel[] = $value->modelid;
        }

        $newmodel = array();
        foreach ($modelid as $key => $value) {
            if (!in_array($value, $oldmodel)) {
                $newmodel[] = array(
                    'userid' => $userid,
                    'modelid' => $value,
                    'createddate' => getCurrentDate(),
                    'createdby' => getCurrentIdUser()
                );
            }
        }

        foreach ($oldmodel as $key => $value) {
            if (!in_array($value, $modelid)) {
                $this->modelsurataccess->delete(array(
                    'userid' => $userid,
                    'modelid' => $value
                ));
            }
        }

        if (count($newmodel) > 0) {
            $this->modelsurataccess->insert_batch($newmodel);
        }

        return JSONResponseDefault('OK', 'Data berhasil disimpan');
    }

    public function letterscode_surat($userid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $accessid = getPost('accessid');

        $get = $this->masterusers->get(array(
            'id' => $userid
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $access = $this->modelsurataccess->select('a.*, b.modelname AS nama')
            ->join('model_surat b', 'b.id = a.modelid')
            ->get(array(
                'a.id' => $accessid,
                'a.userid' => $userid
            ));

        if ($access->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $access->row();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('manageadmin/model/letterscode', array(
                'user' => $get->row(),
                'access' => $row
            ), true)
        ));
    }

    public function process_letterscode_surat($userid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $accessid = getPost('id');
        $letterscode = getPost('letterscode');

        $get = $this->masterusers->get(array(
            'id' => $userid
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $access = $this->modelsurataccess->get(array(
            'id' => $accessid,
            'userid' => $userid
        ));

        if ($access->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $this->modelsurataccess->update(array(
            'id' => $accessid,
            'userid' => $userid
        ), array(
            'letterscode' => $letterscode
        ));

        return JSONResponseDefault('OK', 'Data berhasil disimpan');
    }

    public function customdomain($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->masterusers->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/admin'));
        }

        $row = $get->row();

        $data = array();
        $data['title'] = 'Custom Domain';
        $data['content'] = 'manageadmin/customdomain';
        $data['user'] = $row;
        $data['requestdomain'] = $this->request_domain->result(array(
            'userid' => $id
        ));

        $found_cloudflare = false;
        $valid_cloudflare = false;
        $cloudflare_result = null;

        if ($row->domain != null) {
            $domain = $row->domain;

            if (isDomain($domain)) {
                // Suppress DNS warnings and handle failures gracefully
                $ns = @dns_get_record($domain, DNS_NS);
                // Check if dns_get_record failed and returned false
                if ($ns === false) {
                    $ns = array();
                }
            } else {
                $parts = explode('.', $domain);

                if (count($parts) > 2) {
                    $domain = $parts[count($parts) - 2] . '.' . $parts[count($parts) - 1];

                    // Suppress DNS warnings and handle failures gracefully
                    $ns = @dns_get_record($domain, DNS_NS);
                    // Check if dns_get_record failed and returned false
                    if ($ns === false) {
                        $ns = array();
                    }
                } else {
                    $ns = array();
                }
            }

            // Only iterate if $ns is a valid array
            if (is_array($ns)) {
                foreach ($ns as $key => $value) {
                    if (searchCloudflare($value['target'])) {
                        $found_cloudflare = true;
                    }
                }
            }

            $cloudflare = new Cloudflare(String_Helper::CLOUDLFARE_ACCOUNT, String_Helper::CLOUDFLARE_TOKEN);
            $zone = $cloudflare->zones(array('name' => $domain));

            if (count($zone->result) > 0) {
                $cloudflare_result = $zone->result;
                $valid_cloudflare = true;
            }
        } else {
            $ns = null;
        }

        $data['ns'] = $ns;
        $data['found_cloudflare'] = $found_cloudflare;
        $data['valid_cloudflare'] = $valid_cloudflare;
        $data['cloudflare_result'] = $cloudflare_result;

        return $this->load->view('master', $data);
    }

    public function process_customdomain($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        $get = $this->masterusers->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $domain = getPost('domain');

        $validate = $this->masterusers->get(array(
            'domain' => $domain
        ));

        if ($validate->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Domain sudah digunakan');
        }

        $this->masterusers->update(array(
            'id' => $id
        ), array(
            'domain' => $domain
        ));

        return JSONResponseDefault('OK', 'Data berhasil disimpan');
    }

    public function request_customdomain($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        $get = $this->masterusers->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        $requestpending = $this->request_domain->get(array(
            'userid' => $id,
            'status' => 'Pending'
        ));

        if ($requestpending->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Anda sudah melakukan request domain');
        }

        $this->request_domain->insert(array(
            'userid' => $id,
            'domain' => $row->domain,
            'status' => 'Pending',
            'createddate' => getCurrentDate(),
            'createdby' => getCurrentIdUser()
        ));

        return JSONResponseDefault('OK', 'Data berhasil disimpan');
    }

    public function subdistrict_account()
    {
        $kecamatan = getPost('kecamatan');

        $get = $this->masterusers->get(array(
            'kecamatanid' => $kecamatan,
            'role' => 'kecamatan'
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'MESSAGE' => 'Data ditemukan',
            'ID' => $get->row()->id
        ));
    }
}
