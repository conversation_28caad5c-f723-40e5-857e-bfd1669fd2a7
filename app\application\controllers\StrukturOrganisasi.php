<?php

use PhpOffice\PhpSpreadsheet\Calculation\MathTrig\Base;
use Spipu\Html2Pdf\Html2Pdf;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Struktur_Organisasi $strukturorganisasi
 * @property Datatables $datatables
 * @property CI_Upload $upload
 * @property Setting_Umum $settingumum
 * @property Flow_OrganizationChart $flow_organization_chart
 * @property Master_Users $msusers
 * @property LogStrukurOrganisasi $logstrukturorganisasi
 * @property CI_DB_mysqli_driver $db
 */
class StrukturOrganisasi extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Struktur_Organisasi', 'strukturorganisasi');
        $this->load->model('Setting_Umum', 'settingumum');
        $this->load->model('Flow_OrganizationChart', 'flow_organization_chart');
        $this->load->model('Master_Users', 'msusers');
        $this->load->model('LogStrukturOrganisasi', 'logstrukturorganisasi');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Struktur Organisasi';
        $data['content'] = 'strukturorganisasi/index';
        $data['strukturorganisasi'] = $this->strukturorganisasi->result(array('userid' => getCurrentIdUser()));

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $currentuser = getCurrentUser();
        $datatables = $this->datatables->make('Struktur_Organisasi', 'QueryDatatables', 'SearchDatatables');

        $where = array(
            'a.userid' => getCurrentIdUser(),
        );

        if ($currentuser->sync_siades == 1) {
            $where['a.is_siades'] = 1;
        } else {
            $where["(a.is_siades = 0 OR a.is_siades IS NULL) ="] = true;
        }

        $data = array();
        foreach (
            $datatables->getData($where) as $key => $value
        ) {
            if ($value->foto != null) {
                if ($currentuser->sync_siades == 1) {
                $foto = "<img src=\"" . asset_url_siades($value->foto) . "\" width=\"100\">";
            } else {
                    $foto = "<img src=\"" . asset_url($value->foto) . "\" width=\"100\">";
                }
            } else {
                $foto = "- Foto Tidak Ditemukan -";
            }

            if ($value->priority == 1) {
                $priority = "<span class=\"badge badge-primary\">Tinggi</span>";
            } else if ($value->priority == 2 || $value->priority == null) {
                $priority = "<span class=\"badge badge-warning\">Sedang</span>";
            } else {
                $priority = "<span class=\"badge badge-secondary\">Rendah</span>";
            }

            $nosk = "-";
            if ($value->nosk != null) {
                $tanggalpengangkatan = "";
                if ($value->tanggalpengangkatan) {
                    $tanggalpengangkatan = "(" . date('d F Y', strtotime($value->tanggalpengangkatan)) . ")";
                }

                if ($value->dokumensk == null) {
                    $nosk = $value->nosk . " " . $tanggalpengangkatan;
                } else {
                    $nosk = "<a href=\"" . asset_url($value->dokumensk) . "\" target=\"_blank\">" . $value->nosk . " " . $tanggalpengangkatan . "</a>";
                }
            }

            $subdistrictapproval = "N/A";
            if ($value->subdistrictapproval == null || $value->subdistrictapproval == 'Approved') {
                $subdistrictapproval = "<span class=\"badge badge-success\">Disetujui</span>";
            } else if ($value->subdistrictapproval == "Pending") {
                $subdistrictapproval = "<span class=\"badge badge-warning\">Menunggu Persetujuan Penambahan Anggota</span>";
            } else if ($value->subdistrictapproval == "Pending Edit") {
                $subdistrictapproval = "<span class=\"badge badge-warning\">Menunggu Persetujuan Perubahan Data</span>";
            } else if ($value->subdistrictapproval == "Pending Delete") {
                $subdistrictapproval = "<span class=\"badge badge-warning\">Menunggu Persetujuan Penghapusan Data</span>";
            } else if ($value->subdistrictapproval == "Rejected") {
                $subdistrictapproval = "<span class=\"badge badge-danger\">Penambahan Data Ditolak, $value->rejectreason</span>";
            } else if ($value->subdistrictapproval == "Rejected Edit") {
                $subdistrictapproval = "<span class=\"badge badge-danger\">Perubahan Data Ditolak, $value->rejectreason</span>";
            } else if ($value->subdistrictapproval == "Rejected Delete") {
                $subdistrictapproval = "<span class=\"badge badge-danger\">Penghapusan Data Ditolak, $value->rejectreason</span>";
            }

            $edit = "";
            $delete = "";
            if (($value->subdistrictapproval == null || $value->subdistrictapproval == 'Approved' || $value->subdistrictapproval == 'Rejected' || $value->subdistrictapproval == 'Rejected Edit' || $value->subdistrictapproval == 'Rejected Delete') && $currentuser->sync_siades != 1) {
                $edit = "<a href=\"" . base_url('strukturorganisasi/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm\">
                    <i class=\"fa fa-edit\"></i>
                </a>";

                $delete = "<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteAnggota($value->id)\">
                    <i class=\"fa fa-trash\"></i>
                </button>";
            }

            if ($value->nik != null) {
                $availableMember = getAvailableMember($value->nik);
            } else {
                $availableMember = null;
            }

            $detail = array();
            $detail[] = $foto;
            $detail[] = $value->nik ?? '-';
            $detail[] = $value->nama;
            $detail[] = $value->positionname ?? $value->jabatan;
            $detail[] = $value->gender ?? '-';
            $detail[] = ($value->placeofbirth ?? '-') . ', ' . ($value->dateofbirth ? date('d F Y', strtotime($value->dateofbirth)) : '-');
            $detail[] = $value->type ?? 'Aparat Desa';
            $detail[] = $nosk;
            $detail[] = $priority;
            if ($currentuser->sync_siades != 1) {
                $detail[] = $subdistrictapproval;
            }
            $detail[] = $availableMember == null ? "<span class=\"badge badge-danger\">Tidak Terintegrasi dengan Sistem Absensi Desa</span>" : ($availableMember->status ? ($availableMember->data->available == 1 ? "<span class=\"badge badge-success\">Tersedia</span>" : "<span class=\"badge badge-danger\">Tidak Tersedia - " . $availableMember->data->unavailable_description . "</span>") : "<span class=\"badge badge-danger\">Tidak Terintegrasi dengan Sistem Absensi Desa</span>");
            $detail[] = "<a href=\"https://wa.me/$value->phonenumber\" class=\"btn btn-success btn-sm\">
                <i class=\"fab fa-whatsapp\"></i>
            </a>
            
            $edit
            
            $delete";

            $data[] = $detail;
        }

        return $datatables->json($data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Anggota Organisasi';
        $data['content'] = 'strukturorganisasi/add';
        $data['floworganizationchart'] = $this->flow_organization_chart->result(array('createdby' => getCurrentIdUser()));

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $name = getPost('name');
        $position = getPost('position');
        $task = getPost('task');
        $priority = getPost('priority');
        $nosk = getPost('nosk');
        $tanggalpengangkatan = getPost('tanggalpengangkatan');
        $gender = getPost('gender');
        $placeofbirth = getPost('placeofbirth');
        $dateofbirth = getPost('dateofbirth');
        $phonenumber = getPost('phonenumber');
        $nik = getPost('nik');
        $type = getPost('type');

        if ($nik == null) {
            return JSONResponseDefault('FAILED', 'NIK tidak boleh kosong');
        } else if (strlen($nik) != 16) {
            return JSONResponseDefault('FAILED', 'NIK harus 16 digit');
        } else if ($this->strukturorganisasi->get(array('nik' => $nik))->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'NIK sudah terdaftar');
        } else if ($name == null) {
            return JSONResponseDefault('FAILED', 'Nama tidak boleh kosong');
        } else if ($gender == null) {
            return JSONResponseDefault('FAILED', 'Jenis kelamin tidak boleh kosong');
        } else if ($gender != 'Laki-laki' && $gender != 'Perempuan') {
            return JSONResponseDefault('FAILED', 'Jenis kelamin tidak valid');
        } else if ($placeofbirth == null) {
            return JSONResponseDefault('FAILED', 'Tempat lahir tidak boleh kosong');
        } else if ($dateofbirth == null) {
            return JSONResponseDefault('FAILED', 'Tanggal lahir tidak boleh kosong');
        } else if ($position == null) {
            return JSONResponseDefault('FAILED', 'Jabatan tidak boleh kosong');
        } else if ($phonenumber == null) {
            return JSONResponseDefault('FAILED', 'Nomor telepon tidak boleh kosong');
        } else if ($type == null) {
            return JSONResponseDefault('FAILED', 'Tipe tidak boleh kosong');
        } else if ($task == null) {
            return JSONResponseDefault('FAILED', 'Tugas tidak boleh kosong');
        } else if ($priority == null) {
            return JSONResponseDefault('FAILED', 'Prioritas tidak boleh kosong');
        }

        $insert = array();
        $insert['nik'] = $nik;
        $insert['nama'] = $name;
        $insert['jabatanid'] = $position;
        $insert['tugas'] = $task;
        $insert['userid'] = getCurrentIdUser();
        $insert['priority'] = $priority;
        $insert['nosk'] = $nosk;
        $insert['tanggalpengangkatan'] = $tanggalpengangkatan;
        $insert['gender'] = $gender;
        $insert['placeofbirth'] = $placeofbirth;
        $insert['dateofbirth'] = $dateofbirth;
        $insert['type'] = $type;
        if (substr($phonenumber, 0, 1) == '0') {
            $phonenumber = '62' . substr($phonenumber, 1);
        }
        $insert['phonenumber'] = $phonenumber;
        $insert['subdistrictapproval'] = 'Pending';

        try {
            $upload = doUpload_CloudStorage('picture', 'jpg|jpeg|png');
            $insert['foto'] = $upload['name'];
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }

        if (isset($_FILES['dokumensk']['name']) && $_FILES['dokumensk']['name'] != null) {
            try {
                $upload = doUpload_CloudStorage('dokumensk', 'pdf|doc|docx');
                $insert['dokumensk'] = $upload['name'];
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        }

        $data = $insert;

        $this->strukturorganisasi->insert($insert);
        $strukturorganisasiid = $this->db->insert_id();

        $insert = array();
        $insert['userid'] = getCurrentIdUser();
        $insert['strukturorganisasiid'] = $strukturorganisasiid;
        $insert['log'] = "Desa: Mengajukan penambahan anggota baru dengan nama $name";
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();
        $insert['data'] = json_encode($data);

        $this->logstrukturorganisasi->insert($insert);

        return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->strukturorganisasi->get(array('id' => $id, 'userid' => getCurrentIdUser()));

        if ($get->num_rows() == 0) {
            return redirect(base_url('strukturorganisasi'));
        }

        $data = array();
        $data['title'] = 'Ubah Anggota Organisasi';
        $data['content'] = 'strukturorganisasi/edit';
        $data['anggota'] = $get->row();
        $data['floworganizationchart'] = $this->flow_organization_chart->result(array('createdby' => getCurrentIdUser()));

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->strukturorganisasi->get(array(
            'id' => $id,
            'userid' => getCurrentIdUser(),
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        $name = getPost('name');
        $position = getPost('position');
        $task = getPost('task');
        $priority = getPost('priority');
        $nosk = getPost('nosk');
        $tanggalpengangkatan = getPost('tanggalpengangkatan');
        $gender = getPost('gender');
        $placeofbirth = getPost('placeofbirth');
        $dateofbirth = getPost('dateofbirth');
        $phonenumber = getPost('phonenumber');
        $nik = getPost('nik');
        $type = getPost('type');

        if ($nik == null) {
            return JSONResponseDefault('FAILED', 'NIK tidak boleh kosong');
        } else if (strlen($nik) != 16) {
            return JSONResponseDefault('FAILED', 'NIK harus 16 digit');
        } else if ($get->row()->nik != $nik && $this->strukturorganisasi->get(array('nik' => $nik))->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'NIK sudah terdaftar');
        } else if ($name == null) {
            return JSONResponseDefault('FAILED', 'Nama tidak boleh kosong');
        } else if ($gender == null) {
            return JSONResponseDefault('FAILED', 'Jenis kelamin tidak boleh kosong');
        } else if ($gender != 'Laki-laki' && $gender != 'Perempuan') {
            return JSONResponseDefault('FAILED', 'Jenis kelamin tidak valid');
        } else if ($placeofbirth == null) {
            return JSONResponseDefault('FAILED', 'Tempat lahir tidak boleh kosong');
        } else if ($dateofbirth == null) {
            return JSONResponseDefault('FAILED', 'Tanggal lahir tidak boleh kosong');
        } else if ($position == null) {
            return JSONResponseDefault('FAILED', 'Jabatan tidak boleh kosong');
        } else if ($phonenumber == null) {
            return JSONResponseDefault('FAILED', 'Nomor telepon tidak boleh kosong');
        } else if ($type == null) {
            return JSONResponseDefault('FAILED', 'Tipe tidak boleh kosong');
        } else if ($task == null) {
            return JSONResponseDefault('FAILED', 'Tugas tidak boleh kosong');
        } else if ($priority == null) {
            return JSONResponseDefault('FAILED', 'Prioritas tidak boleh kosong');
        }

        $update = array();
        $update['nama'] = $name;
        $update['jabatanid'] = $position;
        $update['tugas'] = $task;
        $update['userid'] = getCurrentIdUser();
        $update['priority'] = $priority;
        $update['nosk'] = $nosk;
        $update['tanggalpengangkatan'] = $tanggalpengangkatan;
        $update['gender'] = $gender;
        $update['placeofbirth'] = $placeofbirth;
        $update['dateofbirth'] = $dateofbirth;
        $update['rejectreason'] = null;
        if (substr($phonenumber, 0, 1) == '0') {
            $phonenumber = '62' . substr($phonenumber, 1);
        }
        $update['phonenumber'] = $phonenumber;
        $update['nik'] = $nik;
        $update['type'] = $type;

        if (isset($_FILES['picture']['name']) && $_FILES['picture']['name'] != null) {
            try {
                $upload = doUpload_CloudStorage('picture', 'jpg|jpeg|png');
                $update['foto'] = $upload['name'];
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        }

        if (isset($_FILES['dokumensk']['name']) && $_FILES['dokumensk']['name'] != null) {
            try {
                $upload = doUpload_CloudStorage('dokumensk', 'pdf|doc|docx');
                $update['dokumensk'] = $upload['name'];
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        }

        foreach ($update as $key => $value) {
            if ($value != $row->$key) {
                $data_update = json_encode($update);
                $update = array();
                $update['approvaldata'] = $data_update;
                $update['subdistrictapproval'] = 'Pending Edit';
                break;
            }
        }

        $this->strukturorganisasi->update(array('id' => $id), $update);

        $insert = array();
        $insert['userid'] = $row->userid;
        $insert['strukturorganisasiid'] = $id;
        $insert['log'] = "Desa: Mengajukan perubahan data anggota dengan nama $name";
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();
        $insert['data'] = json_encode($update);

        $this->logstrukturorganisasi->insert($insert);

        return JSONResponseDefault('OK', 'Data berhasil diubah');
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->strukturorganisasi->get(array('id' => $id, 'userid' => getCurrentIdUser()));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        if ($row->subdistrictapproval == 'Pending') {
            $this->strukturorganisasi->delete(array('id' => $id));

            $insert = array();
            $insert['userid'] = $row->userid;
            $insert['strukturorganisasiid'] = $id;
            $insert['log'] = "Desa: Melakukan penghapusan data anggota dengan nama $row->nama pada saat masih dalam proses pengajuan";
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();
            $insert['data'] = json_encode($row);

            $this->logstrukturorganisasi->insert($insert);
        } else {
            $this->strukturorganisasi->update(array('id' => $id), array('subdistrictapproval' => 'Pending Delete'));

            $insert = array();
            $insert['userid'] = $row->userid;
            $insert['strukturorganisasiid'] = $id;
            $insert['log'] = "Desa: Mengajukan penghapusan data anggota dengan nama $row->nama";
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->logstrukturorganisasi->insert($insert);
        }

        return JSONResponseDefault('OK', 'Data berhasil dihapus');
    }

    public function download_strukturorganisasi()
    {
        $id = getGet('id');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $pdf = new Html2Pdf('L', 'A3');
        $pdf->setTestTdInOnePage(false);

        $setting = $this->settingumum->get(array(
            'id_user' => $id ?? getCurrentIdUser(),
        ))->row();

        $currentuser = getCurrentUser($id);

        $where = array(
            'a.userid' => $this->subdomain_account->id,
        );

        if ($currentuser->sync_siades == 1) {
            $where['a.is_siades'] = 1;
        } else {
            $where["(a.is_siades = 0 OR a.is_siades IS NULL) ="] = true;
        }

        $pdf->writeHTML($this->load->view('strukturorganisasi', array(
            'setting' => $setting,
            'strukturorganisasi' => $this->strukturorganisasi->select('a.*, b.name AS positionname')
                ->join('floworganizationchart b', 'b.id = a.jabatanid', 'LEFT')
                ->order_by('a.priority', 'ASC')
                ->result($where),
        ), true));
        $pdf->output('Struktur Organisasi.pdf', 'I');
    }

    public function permohonan()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isKecamatan()) {
            return redirect(base_url('dashboard'));
        }

        $currentuser = $this->msusers->get(array('id' => getCurrentIdUser()))->row();

        $data = array();
        $data['title'] = 'Permohonan Perubahan Data Struktur Organisasi';
        $data['content'] = 'strukturorganisasi/permohonan';
        $data['permohonan'] = $this->msusers->select('a.id, c.nama_kelurahan, b.total')
            ->join("(select userid, count(*) as total from strukturorganisasi where subdistrictapproval is not null and subdistrictapproval != 'Approved' and subdistrictapproval != 'Rejected' and subdistrictapproval != 'Rejected Edit' and subdistrictapproval != 'Rejected Delete' group by userid) b", 'b.userid = a.id')
            ->join('kelurahan c', 'c.id_kelurahan = a.kelurahanid')
            ->order_by('b.total', 'DESC')
            ->result(array(
                'c.id_kecamatan' => $currentuser->kecamatanid
            ));

        return $this->load->view('master', $data);
    }

    public function permohonan_detail()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isKecamatan()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $currentuser = $this->msusers->get(array('id' => getCurrentIdUser()))->row();
        $id = getPost('id');

        $get = $this->msusers->select('a.*, b.nama_kelurahan')
            ->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
            ->get(array(
                'a.id' => $id,
                'b.id_kecamatan' => $currentuser->kecamatanid
            ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $strukturorganisasi = $this->strukturorganisasi->select('a.*, b.name AS positionname')
            ->join('floworganizationchart b', 'b.id = a.jabatanid', 'LEFT')
            ->result(array(
                'a.userid' => $id,
                "(a.subdistrictapproval is not null and a.subdistrictapproval != 'Approved' and a.subdistrictapproval != 'Rejected' and a.subdistrictapproval != 'Rejected Edit' and a.subdistrictapproval != 'Rejected Delete') =" => true
            ));

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('strukturorganisasi/permohonan_detail', array(
                'permohonan' => $get->row(),
                'strukturorganisasi' => $strukturorganisasi,
            ), true)
        ));
    }

    public function permohonan_approve()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isKecamatan()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->strukturorganisasi->get(array(
            'id' => $id,
            "(subdistrictapproval = 'Pending' OR subdistrictapproval = 'Pending Edit' OR subdistrictapproval = 'Pending Delete') =" => true
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        if ($row->subdistrictapproval == 'Pending') {
            $this->strukturorganisasi->update(array('id' => $id), array(
                'subdistrictapproval' => 'Approved'
            ));

            $insert = array();
            $insert['userid'] = $row->userid;
            $insert['strukturorganisasiid'] = $id;
            $insert['log'] = "Kecamatan: Menyetujui penambahan anggota baru dengan nama $row->nama";
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();
            $insert['data'] = json_encode($row);

            $this->logstrukturorganisasi->insert($insert);
        } else if ($row->subdistrictapproval == 'Pending Edit') {
            $approvaldata = json_decode($row->approvaldata, true);
            if (isset($approvaldata['nik'])) {
                $get = $this->strukturorganisasi->get(array(
                    'nik' => $approvaldata['nik'],
                    'id !=' => $id,
                ));

                if ($get->num_rows() > 0) {
                    return JSONResponseDefault('FAILED', 'NIK sudah terdaftar');
                }
            }

            $approvaldata['subdistrictapproval'] = 'Approved';

            $this->strukturorganisasi->update(array('id' => $id), $approvaldata);

            $insert = array();
            $insert['userid'] = $row->userid;
            $insert['strukturorganisasiid'] = $id;
            $insert['log'] = "Kecamatan: Menyetujui perubahan data anggota dengan nama $row->nama";
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();
            $insert['data'] = json_encode($row);

            $this->logstrukturorganisasi->insert($insert);
        } else if ($row->subdistrictapproval == 'Pending Delete') {
            $this->strukturorganisasi->delete(array('id' => $id));

            $insert = array();
            $insert['userid'] = $row->userid;
            $insert['strukturorganisasiid'] = $id;
            $insert['log'] = "Kecamatan: Menyetujui penghapusan data anggota dengan nama $row->nama";
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();
            $insert['data'] = json_encode($row);

            $this->logstrukturorganisasi->insert($insert);
        } else {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        return JSONResponseDefault('OK', 'Data berhasil disetujui');
    }

    public function permohonan_reject()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isKecamatan()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->strukturorganisasi->get(array(
            'id' => $id,
            "(subdistrictapproval = 'Pending' OR subdistrictapproval = 'Pending Edit' OR subdistrictapproval = 'Pending Delete') =" => true
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('strukturorganisasi/permohonan_reject', array(
                'id' => $id,
                'permohonan' => $get->row()
            ), true)
        ));
    }

    public function permohonan_reject_process()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isKecamatan()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');
        $reason = getPost('reason');

        $get = $this->strukturorganisasi->get(array(
            'id' => $id,
            "(subdistrictapproval = 'Pending' OR subdistrictapproval = 'Pending Edit' OR subdistrictapproval = 'Pending Delete') =" => true
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        if ($row->subdistrictapproval == 'Pending') {
            $subdistrictapproval = 'Rejected';
        } else if ($row->subdistrictapproval == 'Pending Edit') {
            $subdistrictapproval = 'Rejected Edit';
        } else if ($row->subdistrictapproval == 'Pending Delete') {
            $subdistrictapproval = 'Rejected Delete';
        } else {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $this->strukturorganisasi->update(array('id' => $id), array(
            'subdistrictapproval' => $subdistrictapproval,
            'rejectreason' => $reason
        ));

        $insert = array();
        $insert['userid'] = $row->userid;
        $insert['strukturorganisasiid'] = $id;
        $insert['log'] = "Kecamatan: Menolak pengajuan perubahan data anggota dengan nama $row->nama, alasan: $reason";
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();
        $insert['data'] = json_encode($row);

        $this->logstrukturorganisasi->insert($insert);

        return JSONResponseDefault('OK', 'Data berhasil ditolak');
    }

    public function sync()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        }

        $currentuser = getCurrentUser();
        $organizationalstructure = getOrganizationalStructure($currentuser->username_siades);

        if (isset($organizationalstructure->status) && $organizationalstructure->status) {
            $data = $organizationalstructure->data;

            $available_data = array();
            foreach ($data as $key => $value) {
                $exec = array();
                $exec['nama'] = $value->name;
                $exec['jabatan'] = $value->position;
                $exec['tugas'] = $value->task;
                $exec['foto'] = $value->images;
                $exec['userid'] = $currentuser->id;
                $exec['priority'] = $value->priority;
                $exec['nosk'] = $value->nosk;
                $exec['tanggalpengangkatan'] = $value->appointmentdate;
                $exec['dokumensk'] = $value->documentsk;
                $exec['phonenumber'] = $value->phonenumber;
                $exec['gender'] = $value->gender;
                $exec['placeofbirth'] = $value->placebirth;
                $exec['dateofbirth'] = $value->datebirth;
                $exec['nik'] = $value->nik;
                $exec['type'] = $value->type;
                $exec['is_siades'] = 1;

                $get = $this->strukturorganisasi->get(array('nik' => $value->nik));

                if ($get->num_rows() == 0) {
                    $this->strukturorganisasi->insert($exec);
                } else {
                    $this->strukturorganisasi->update(array('nik' => $value->nik), $exec);
                }

                $available_data[] = $value->nik;
            }

            $this->strukturorganisasi->where_not_in('nik', $available_data)->where(array('userid' => $currentuser->id))->delete();

            return JSONResponseDefault('OK', 'Data berhasil disinkronisasi');
        } else {
            if (isset($organizationalstructure->message)) {
                return JSONResponseDefault('FAILED', $organizationalstructure->message);
            } else {
                return JSONResponseDefault('FAILED', 'Gagal mengambil data dari Sistem Absensi Desa');
            }
        }
    }
}
