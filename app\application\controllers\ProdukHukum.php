<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Produk_Hukum $produkhukum
 * @property Datatables $datatables
 * @property Kategoridokumens $kategoridokumens
 * @property CI_Upload $upload
 * @property Setting_Umum $settingumum
 * @property Kontak_Penting $kontakpenting
 */
class ProdukHukum extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Produk_Hukum', 'produkhukum');
        $this->load->model('Master_Users', 'masterusers');
        $this->load->model('Setting_Umum', 'settingumum');
        $this->load->model('Kontak_Penting', 'kontakpenting');
        $this->load->model('Kategoridokumens', 'kategoridokumens');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = 'Produk Hukum';
        $data['content'] = 'produkhukum/index';
        $data['produkhukum'] = $this->produkhukum->select('a.*, b.name AS categoryname')
            ->join('kategoridokumen b', 'b.id = a.categoryid', 'LEFT')
            ->result(array('a.userid' => getCurrentIdUser()));

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $datatables = $this->datatables->make('Produk_Hukum', 'QueryDatatables', 'SearchDatatables');

        $data = array();
        foreach ($datatables->getData(array('a.userid' => getCurrentIdUser())) as $key => $value) {
            $detail = array();
            $detail[] = DateFormat($value->tanggal_penerbitan, 'd F Y H:i:s');
            $detail[] = $value->nama_produk;
            $detail[] = $value->categoryname ?? '-';
            $detail[] = IDR($value->download_count);
            $detail[] = "<a href=\"" . asset_url($value->link_download) . "\" target=\"_blank\" class=\"btn btn-primary btn-sm\">
                <i class=\"fa fa-download\"></i>
            </a>

            <a href=\"" . base_url('produkhukum/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm\">
                <i class=\"fa fa-edit\"></i>
            </a>

            <button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteProduk($value->id)\">
                <i class=\"fa fa-trash\"></i>
            </button>";

            $data[] = $detail;
        }

        return $datatables->json($data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Produk Hukum';
        $data['content'] = 'produkhukum/add';
        $data['kategori'] = $this->kategoridokumens->result(array(
            'userid' => getCurrentIdUser()
        ));

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $name = getPost('name');
        $categoryid = getPost('categoryid');

        $insert = array();
        $insert['tanggal_penerbitan'] = getCurrentDate();
        $insert['nama_produk'] = $name;
        $insert['categoryid'] = $categoryid;
        $insert['download_count'] = 0;
        $insert['userid'] = getCurrentIdUser();

        try {
            $upload = doUpload_CloudStorage('dokumen', 'word|pdf');
            $insert['link_download'] = $upload['name'];
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }

        $this->produkhukum->insert($insert);

        return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->produkhukum->get(array('id' => $id, 'userid' => getCurrentIdUser()));

        if ($get->num_rows() == 0) {
            return redirect(base_url('produkhukum'));
        }

        $row = $get->row();

        $data = array();
        $data['title'] = 'Ubah Produk Hukum';
        $data['content'] = 'produkhukum/edit';
        $data['produkhukum'] = $row;
        $data['kategori'] = $this->kategoridokumens->result(array(
            'userid' => getCurrentIdUser()
        ));

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $name = getPost('name');
        $categoryid = getPost('categoryid');

        $update = array();
        $update['nama_produk'] = $name;
        $update['categoryid'] = $categoryid;

        if (isset($_FILES['dokumen']['size']) && $_FILES['dokumen']['size'] > 0) {
            try {
                $upload = doUpload_CloudStorage('dokumen', 'word|pdf');
                $update['link_download'] = $upload['name'];
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        }

        $this->produkhukum->update(array('id' => $id, 'userid' => getCurrentIdUser()), $update);

        return JSONResponseDefault('OK', 'Data berhasil diubah');
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $this->produkhukum->delete(array('id' => $id, 'userid' => getCurrentIdUser()));

        return JSONResponseDefault('OK', 'Data berhasil dihapus');
    }

    public function produkhukum()
    {
        $data = array();

        $where = array(
            'a.id_user' => $this->subdomain_account->id
        );

        $setting = $this->settingumum->getDefaultData($where);
        $data['title'] = 'Produk Hukum';
        $data['setting'] = $setting->row();
        $data['kontakpenting'] = $this->kontakpenting->getDefaultData($where)->result();
        $data['kategori'] = $this->kategoridokumens->result(array(
            'userid' => $this->subdomain_account->id
        ));

        if ($setting->num_rows() > 0) {
            // ProGides platform always uses ProGides theme regardless of theme setting
            if (getPlatformName() == 'ProGides') {
                $data['content'] = 'progides/produk_hukum';
                return $this->load->view('progides/master', $data);
            } elseif ($this->subdomain_account->themeid == 2) {
                $data['content'] = 'profile/produkhukum';
                return $this->load->view('profile/master', $data);
            } else {
                $data['content'] = 'landing/produkhukum';
                return $this->load->view('landing/master', $data);
            }
        } else {
            return $this->load->view('profile_v2/configuration', $data);
        }
    }

    public function download_produkhukum($id)
    {
        $get = $this->produkhukum->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return redirect(base_url());
        }

        $row = $get->row();

        $update = array();
        $update['download_count'] = $row->download_count + 1;

        $this->produkhukum->update(array('id' => $id), $update);

        return redirect(asset_url($row->link_download));
    }

    public function detail_produkhukum($id)
    {
        $where = array(
            'a.id_user' => $this->subdomain_account->id
        );

        $get = $this->kategoridokumens->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return redirect(base_url('produk/hukum'));
        }

        $setting = $this->settingumum->getDefaultData($where);

        $data = array();
        $data['title'] = 'Detail Produk Hukum';
        $data['setting'] = $setting->row();
        $data['content'] = 'landing/detail_produkhukum';
        $data['produkhukum'] = $this->produkhukum->result(array(
            'a.categoryid' => $id
        ));
        $data['kontakpenting'] = $this->kontakpenting->getDefaultData($where)->result();

        return $this->load->view('landing/master', $data);
    }

    public function detail()
    {
        $id = getPost('id');

        $produkhukum = array(
            'userid' => $this->subdomain_account->id,
            'categoryid' => $id
        );

        $data = array();
        $data['produkhukum'] = $this->produkhukum->result($produkhukum);

        // Use ProGides view if platform is ProGides
        $view_path = 'profile/produkhukum_detail';
        if (getPlatformName() == 'ProGides') {
            $view_path = 'progides/produkhukum_detail';
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view($view_path, $data, true)
        ));
    }
}
