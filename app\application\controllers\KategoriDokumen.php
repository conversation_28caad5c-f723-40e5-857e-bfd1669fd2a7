<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Datatables $datatables
 */
class KategoriDokumen extends MY_Controller
{
    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Kategori Dokumen';
        $data['content'] = 'kategori_dokumen/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $datatables = $this->datatables->make('Kategoridokumens', 'QueryDatatables', 'SearchDatatables');

        $data = array();
        foreach ($datatables->getData(array('userid' => getCurrentIdUser())) as $key => $value) {
            $detail = array();
            $detail[] = $value->name;
            $detail[] = $value->icon != null ? '<img src="' . asset_url($value->icon) . '" class="img-fluid" style="max-width: 50px">' : '-';
            $detail[] = "<a href=\"" . base_url('master/kategoridokumen/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm mb-1\">
                <i class=\"fa fa-edit\"></i>
            </a>
            
            <button type=\"button\" class=\"btn btn-danger btn-sm mb-1\" onclick=\"deleteData('$value->name', '$value->id')\">
                <i class=\"fa fa-trash\"></i>
            </button>";

            $data[] = $detail;
        }

        return $datatables->json($data);
    }
}
